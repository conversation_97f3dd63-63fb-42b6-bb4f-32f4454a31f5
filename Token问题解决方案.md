# 🔑 预约人姓名记录为"游客"的真正原因和解决方案

## 🎯 问题根本原因

通过深入分析后端代码，发现问题的真正原因是：

### ❌ **后端使用自动填充机制**

后端有一个 `MyMetaObjectHandler` 类，会自动填充 `createBy` 和 `createTime` 字段：

```java
@Component
public class MyMetaObjectHandler implements MetaObjectHandler {
    
    @Override
    public void insertFill(MetaObject metaObject) {
        String id = TokenUtils.getUserIdByToken();  // 🔑 关键：从Token获取用户ID
        User user = userService.getById(id);
        metaObject.setValue("createTime", new Date());
        metaObject.setValue("updateTime", new Date());
        if (user != null) {
            metaObject.setValue("createBy", user.getUserName());  // 🎯 这里设置创建者
            metaObject.setValue("updateBy", user.getUserName());
        }
    }
}
```

### ❌ **前端Token问题**

1. **Token未正确发送**：前端可能没有有效的Token
2. **Token已过期**：Token可能已经失效
3. **Token格式错误**：Token格式不正确

## 🔍 诊断步骤

### 第一步：检查Token状态
```javascript
// 在浏览器控制台执行
console.log('Token:', localStorage.getItem('user_token'));
console.log('User Info:', localStorage.getItem('user_info'));
```

### 第二步：使用调试页面
访问：`http://127.0.0.1:3002/#/user-info-debug`

检查：
- ✅ 是否有Token
- ✅ Token是否有效
- ✅ 用户信息是否完整

### 第三步：检查网络请求
1. 打开浏览器开发者工具
2. 切换到Network标签页
3. 提交预约请求
4. 查看请求头中是否包含 `x_access_token`

## 🛠️ 解决方案

### 方案1：确保正确登录 🔑

#### 1.1 重新登录
```javascript
// 清除现有登录信息
localStorage.removeItem('user_token');
localStorage.removeItem('user_info');

// 重新登录系统
// 确保登录成功后有Token和用户信息
```

#### 1.2 检查登录响应
确保登录API返回正确的Token格式：
```javascript
// 登录成功后应该有：
{
  code: 200,
  data: {
    token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",  // JWT Token
    user: { ... }  // 用户信息
  }
}
```

### 方案2：手动设置有效Token 🧪

如果需要测试，可以手动设置Token：

```javascript
// 获取一个有效的Token（从正常登录的用户那里复制）
const validToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...";

// 设置Token和用户信息
localStorage.setItem('user_token', validToken);
localStorage.setItem('user_info', JSON.stringify({
  id: '0de96461b6ef0328cef416dea9366c9c',
  user_name: '杭州水果捞',
  login_account: 'user',
  email: '<EMAIL>'
}));
```

### 方案3：修复前端Token处理 🔧

检查 `src/utils/request.js` 中的Token处理：

```javascript
// 确保请求拦截器正确添加Token
config.headers.x_access_token = window.localStorage.getItem("user_token")
```

### 方案4：后端Token验证 🔍

如果Token有问题，后端会：
1. 无法从Token中获取用户ID
2. 无法查询到用户信息
3. `createBy` 字段为空或默认值

## 📋 完整的测试流程

### 1. 登录测试
```javascript
// 1. 访问登录页面
// 2. 输入正确的用户名密码
// 3. 检查登录响应
// 4. 确认Token和用户信息已保存
```

### 2. Token验证测试
```javascript
// 在控制台执行
fetch('/api/user/info', {
  headers: {
    'x_access_token': localStorage.getItem('user_token')
  }
}).then(res => res.json()).then(data => {
  console.log('Token验证结果:', data);
});
```

### 3. 预约功能测试
```javascript
// 1. 确保Token有效
// 2. 访问景点详情页面
// 3. 提交预约
// 4. 检查数据库记录
```

## 🎯 预期结果

修复后的流程：
1. ✅ 用户正确登录，获得有效Token
2. ✅ 前端发送请求时携带Token
3. ✅ 后端从Token中获取用户ID
4. ✅ 后端查询用户信息，获得真实用户名
5. ✅ 自动填充机制设置正确的 `createBy` 字段

## 🚨 常见问题排查

### 问题1：Token为空
**原因**：登录失败或登录响应格式错误
**解决**：检查登录API和响应处理

### 问题2：Token无效
**原因**：Token过期或格式错误
**解决**：重新登录获取新Token

### 问题3：后端无法解析Token
**原因**：Token格式不正确或密钥不匹配
**解决**：检查JWT配置和密钥

### 问题4：用户信息查询失败
**原因**：Token中的用户ID不存在
**解决**：检查用户数据和Token生成逻辑

## 🔧 快速修复命令

### 清除并重新登录
```javascript
// 在控制台执行
localStorage.clear();
location.reload();
// 然后重新登录
```

### 设置测试Token
```javascript
// 使用已知有效的Token
localStorage.setItem('user_token', 'YOUR_VALID_TOKEN_HERE');
localStorage.setItem('user_info', JSON.stringify({
  id: '0de96461b6ef0328cef416dea9366c9c',
  user_name: '杭州水果捞',
  login_account: 'user'
}));
```

## 📊 验证方法

### 1. 检查请求头
在Network标签页中查看预约请求：
```
Headers:
x_access_token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### 2. 检查后端日志
后端应该输出：
```
公共字段自动填充[insert]...
用户ID: 0de96461b6ef0328cef416dea9366c9c
用户名: 杭州水果捞
```

### 3. 检查数据库记录
```sql
SELECT create_by, create_time, user_id 
FROM sys_attraction_order 
ORDER BY create_time DESC 
LIMIT 1;
```

应该显示：
```
create_by: 杭州水果捞
create_time: 2024-12-20 10:30:00
user_id: 0de96461b6ef0328cef416dea9366c9c
```

---

**总结**：问题的根本原因是Token问题，不是前端代码逻辑问题。后端依赖Token来获取用户信息并自动填充创建者字段。确保Token有效是解决问题的关键！
