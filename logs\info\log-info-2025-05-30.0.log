2025-05-30 17:58:15.447 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Starting BackendApplication using Java 17.0.1 on LAPTOP-B4MIU7N4 with PID 26092 (D:\jiasu\<PERSON>ian<PERSON><PERSON>yun\re-travel\tourism-backend\target\classes started by 靓仔 in D:\jiasu\TianYiyun\re-travel\tourism-backend)
2025-05-30 17:58:15.453 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - The following profiles are active: dev
2025-05-30 17:58:15.503 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-30 17:58:15.503 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-30 17:58:16.041 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-05-30 17:58:16.043 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-30 17:58:16.068 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-05-30 17:58:16.626 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-05-30 17:58:16.634 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-30 17:58:16.636 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-30 17:58:16.636 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-05-30 17:58:16.704 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-30 17:58:16.705 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1201 ms
2025-05-30 17:58:17.053 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-30 17:58:17.540 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-30 17:58:17.565 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-30 17:58:17.582 [Druid-ConnectionPool-Create-1994796227] INFO  com.alibaba.druid.pool.DruidAbstractDataSource - {dataSource-1} failContinuous is true
2025-05-30 17:58:17.583 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-05-30 17:58:39.973 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Starting BackendApplication using Java 17.0.1 on LAPTOP-B4MIU7N4 with PID 21804 (D:\jiasu\TianYiyun\re-travel\tourism-backend\target\classes started by 靓仔 in D:\jiasu\TianYiyun\re-travel\tourism-backend)
2025-05-30 17:58:39.976 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - The following profiles are active: dev
2025-05-30 17:58:40.027 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-30 17:58:40.027 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-30 17:58:40.569 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-05-30 17:58:40.572 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-30 17:58:40.598 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-05-30 17:58:41.171 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-05-30 17:58:41.181 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-30 17:58:41.181 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-30 17:58:41.181 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-05-30 17:58:41.260 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-30 17:58:41.261 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1233 ms
2025-05-30 17:58:41.625 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-30 17:58:41.985 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-30 17:58:42.003 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-30 17:58:42.023 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-05-30 17:58:42.025 [Druid-ConnectionPool-Create-*********] INFO  com.alibaba.druid.pool.DruidAbstractDataSource - {dataSource-1} failContinuous is true
2025-05-30 18:13:44.177 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Starting BackendApplication using Java 17.0.1 on LAPTOP-B4MIU7N4 with PID 26224 (D:\jiasu\TianYiyun\re-travel\tourism-backend\target\classes started by 靓仔 in D:\jiasu\TianYiyun\re-travel\tourism-backend)
2025-05-30 18:13:44.179 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - The following profiles are active: dev
2025-05-30 18:13:44.220 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-30 18:13:44.221 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-30 18:13:44.809 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-05-30 18:13:44.811 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-30 18:13:44.840 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-05-30 18:13:45.388 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-05-30 18:13:45.399 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-30 18:13:45.400 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-30 18:13:45.400 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-05-30 18:13:45.474 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-30 18:13:45.475 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1254 ms
2025-05-30 18:13:45.826 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-30 18:13:46.541 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-30 18:13:47.299 [restartedMain] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-05-30 18:13:47.511 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-05-30 18:13:47.540 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-30 18:13:47.566 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-05-30 18:13:47.575 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Started BackendApplication in 3.831 seconds (JVM running for 4.572)
2025-05-30 18:13:47.577 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - =====================项目后端启动成功============================
2025-05-30 18:13:56.586 [Thread-5] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-05-30 18:13:56.587 [Thread-5] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-05-30 18:13:56.593 [Thread-5] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-05-30 18:13:56.698 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Starting BackendApplication using Java 17.0.1 on LAPTOP-B4MIU7N4 with PID 26224 (D:\jiasu\TianYiyun\re-travel\tourism-backend\target\classes started by 靓仔 in D:\jiasu\TianYiyun\re-travel\tourism-backend)
2025-05-30 18:13:56.700 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - The following profiles are active: dev
2025-05-30 18:13:56.846 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-05-30 18:13:56.846 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-30 18:13:56.847 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 0 ms. Found 0 Redis repository interfaces.
2025-05-30 18:13:56.932 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-05-30 18:13:56.932 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-30 18:13:56.933 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-30 18:13:56.933 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-05-30 18:13:56.951 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-30 18:13:56.952 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 248 ms
2025-05-30 18:13:56.995 [restartedMain] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-05-30 18:13:57.025 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-30 18:13:57.146 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
2025-05-30 18:13:57.201 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-05-30 18:13:57.204 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-05-30 18:13:57.204 [restartedMain] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-05-30 18:13:57.204 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-05-30 18:13:57.208 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-05-30 18:14:00.147 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Starting BackendApplication using Java 17.0.1 on LAPTOP-B4MIU7N4 with PID 26224 (D:\jiasu\TianYiyun\re-travel\tourism-backend\target\classes started by 靓仔 in D:\jiasu\TianYiyun\re-travel\tourism-backend)
2025-05-30 18:14:00.148 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - The following profiles are active: dev
2025-05-30 18:14:00.285 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-05-30 18:14:00.285 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-30 18:14:00.296 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
2025-05-30 18:14:00.368 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-05-30 18:14:00.368 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-30 18:14:00.368 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-30 18:14:00.368 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-05-30 18:14:00.391 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-30 18:14:00.391 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 242 ms
2025-05-30 18:14:00.420 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-30 18:14:00.537 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} inited
2025-05-30 18:14:00.831 [restartedMain] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-05-30 18:14:00.888 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-05-30 18:14:00.910 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-30 18:14:00.922 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-05-30 18:14:00.925 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Started BackendApplication in 0.803 seconds (JVM running for 17.922)
2025-05-30 18:14:00.926 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-05-30 18:14:00.927 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - =====================项目后端启动成功============================
2025-05-30 18:14:50.850 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-30 18:14:50.850 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-30 18:14:50.851 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-30 18:34:16.244 [http-nio-8080-exec-12] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-30 18:34:16.259 [http-nio-8080-exec-12] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[insert]...
2025-05-30 18:34:27.570 [http-nio-8080-exec-20] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-30 18:37:20.890 [http-nio-8080-exec-13] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-30 19:15:19.660 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-05-30 19:15:19.664 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-05-30 19:15:19.665 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
