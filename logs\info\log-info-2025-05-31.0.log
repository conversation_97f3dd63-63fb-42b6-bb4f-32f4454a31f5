2025-05-31 10:20:19.537 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Starting BackendApplication using Java 17.0.1 on LAPTOP-B4MIU7N4 with PID 23460 (D:\jiasu\<PERSON>ian<PERSON><PERSON>yun\re-travel\tourism-backend\target\classes started by 靓仔 in D:\jiasu\TianYiyun\re-travel\tourism-backend)
2025-05-31 10:20:19.543 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - The following profiles are active: dev
2025-05-31 10:20:19.599 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-05-31 10:20:19.599 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-05-31 10:20:20.235 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-05-31 10:20:20.237 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-05-31 10:20:20.267 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-05-31 10:20:20.830 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-05-31 10:20:20.840 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-31 10:20:20.840 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-05-31 10:20:20.840 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-05-31 10:20:20.917 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-31 10:20:20.917 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1317 ms
2025-05-31 10:20:21.269 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-05-31 10:20:21.943 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-05-31 10:20:22.592 [restartedMain] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-05-31 10:20:22.816 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-05-31 10:20:22.844 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-31 10:20:22.873 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-05-31 10:20:22.882 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Started BackendApplication in 3.815 seconds (JVM running for 4.592)
2025-05-31 10:20:22.885 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - =====================项目后端启动成功============================
2025-05-31 10:20:33.998 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-05-31 10:20:33.998 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-05-31 10:20:33.999 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-05-31 14:15:01.413 [http-nio-8080-exec-63] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 14:15:42.088 [http-nio-8080-exec-96] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 14:24:05.025 [http-nio-8080-exec-74] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 14:24:17.747 [http-nio-8080-exec-77] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 14:24:27.118 [http-nio-8080-exec-80] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 14:24:54.134 [http-nio-8080-exec-22] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 14:39:08.611 [http-nio-8080-exec-34] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 14:39:48.942 [http-nio-8080-exec-53] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 14:39:53.278 [http-nio-8080-exec-59] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 14:42:12.234 [http-nio-8080-exec-86] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 14:42:14.650 [http-nio-8080-exec-94] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 15:03:47.963 [http-nio-8080-exec-25] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 15:03:47.969 [http-nio-8080-exec-25] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[insert]...
2025-05-31 15:04:06.596 [http-nio-8080-exec-40] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 15:06:36.306 [http-nio-8080-exec-69] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 15:06:37.799 [http-nio-8080-exec-70] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 15:06:57.708 [http-nio-8080-exec-75] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 15:07:11.541 [http-nio-8080-exec-76] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 15:30:26.758 [http-nio-8080-exec-26] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[insert]...
2025-05-31 15:34:25.527 [http-nio-8080-exec-74] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 15:57:47.690 [http-nio-8080-exec-9] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[insert]...
2025-05-31 15:57:52.044 [http-nio-8080-exec-12] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[insert]...
2025-05-31 15:57:53.287 [http-nio-8080-exec-16] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[insert]...
2025-05-31 17:21:06.590 [http-nio-8080-exec-73] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[insert]...
2025-05-31 18:24:33.924 [http-nio-8080-exec-30] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-05-31 18:24:53.564 [http-nio-8080-exec-34] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
