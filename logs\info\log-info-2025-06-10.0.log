2025-06-10 11:11:25.170 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Starting BackendApplication using Java 17.0.1 on LAPTOP-B4MIU7N4 with PID 7728 (D:\jiasu\<PERSON><PERSON><PERSON><PERSON>yun\re-travel\tourism-backend\target\classes started by 靓仔 in D:\jiasu\TianYiyun\re-travel\tourism-backend)
2025-06-10 11:11:25.175 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - The following profiles are active: dev
2025-06-10 11:11:25.238 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-10 11:11:25.238 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-10 11:11:25.882 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-10 11:11:25.885 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-10 11:11:25.919 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 20 ms. Found 0 Redis repository interfaces.
2025-06-10 11:11:26.511 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-10 11:11:26.520 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-10 11:11:26.520 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-10 11:11:26.521 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-10 11:11:26.595 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-10 11:11:26.595 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1357 ms
2025-06-10 11:11:26.994 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-10 11:11:27.660 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-10 11:11:28.334 [restartedMain] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-06-10 11:11:28.554 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-10 11:11:28.584 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-10 11:11:28.612 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-10 11:11:28.621 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Started BackendApplication in 3.887 seconds (JVM running for 4.633)
2025-06-10 11:11:28.623 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - =====================项目后端启动成功============================
2025-06-10 11:13:45.915 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-10 11:13:45.915 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-10 11:13:45.915 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 0 ms
2025-06-10 11:14:21.343 [http-nio-8080-exec-33] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-06-10 11:14:22.956 [http-nio-8080-exec-38] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-06-10 11:15:55.612 [http-nio-8080-exec-22] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [1749390907] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-06-10 11:17:11.496 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-10 11:17:11.498 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-10 11:17:11.505 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
