2025-06-11 10:43:52.131 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Starting BackendApplication using Java 17.0.1 on LAPTOP-B4MIU7N4 with PID 20324 (D:\jiasu\<PERSON>ian<PERSON><PERSON>yun\by-travel\tourism-backend\target\classes started by 靓仔 in D:\jiasu\TianYiyun\by-travel\tourism-backend)
2025-06-11 10:43:52.138 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - The following profiles are active: dev
2025-06-11 10:43:52.189 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-11 10:43:52.191 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-11 10:43:52.866 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-11 10:43:52.868 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 10:43:52.898 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-06-11 10:43:53.663 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-11 10:43:53.673 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-11 10:43:53.674 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:43:53.674 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-11 10:43:53.771 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 10:43:53.771 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1580 ms
2025-06-11 10:43:54.168 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 10:43:54.921 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 10:43:55.810 [restartedMain] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-06-11 10:43:56.132 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-11 10:43:56.174 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-11 10:43:56.216 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-11 10:43:56.228 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Started BackendApplication in 4.638 seconds (JVM running for 5.55)
2025-06-11 10:43:56.231 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - =====================项目后端启动成功============================
2025-06-11 10:43:58.660 [Thread-5] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-11 10:43:58.662 [Thread-5] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-11 10:43:58.670 [Thread-5] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 10:43:58.807 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Starting BackendApplication using Java 17.0.1 on LAPTOP-B4MIU7N4 with PID 20324 (D:\jiasu\TianYiyun\by-travel\tourism-backend\target\classes started by 靓仔 in D:\jiasu\TianYiyun\by-travel\tourism-backend)
2025-06-11 10:43:58.808 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - The following profiles are active: dev
2025-06-11 10:43:58.978 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-11 10:43:58.979 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 10:43:58.980 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 Redis repository interfaces.
2025-06-11 10:43:59.072 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-11 10:43:59.073 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-11 10:43:59.073 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:43:59.073 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-11 10:43:59.103 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 10:43:59.103 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 291 ms
2025-06-11 10:43:59.167 [restartedMain] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-06-11 10:43:59.209 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 10:43:59.385 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} inited
2025-06-11 10:43:59.475 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2025-06-11 10:43:59.480 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2025-06-11 10:43:59.481 [restartedMain] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-11 10:43:59.481 [restartedMain] INFO  org.apache.catalina.core.StandardService - Stopping service [Tomcat]
2025-06-11 10:43:59.489 [restartedMain] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-06-11 10:44:03.875 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Starting BackendApplication using Java 17.0.1 on LAPTOP-B4MIU7N4 with PID 20324 (D:\jiasu\TianYiyun\by-travel\tourism-backend\target\classes started by 靓仔 in D:\jiasu\TianYiyun\by-travel\tourism-backend)
2025-06-11 10:44:03.875 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - The following profiles are active: dev
2025-06-11 10:44:04.017 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-11 10:44:04.017 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 10:44:04.029 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
2025-06-11 10:44:04.118 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-11 10:44:04.119 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-11 10:44:04.119 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:44:04.119 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-11 10:44:04.148 [restartedMain] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 10:44:04.148 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 271 ms
2025-06-11 10:44:04.185 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 10:44:04.311 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} inited
2025-06-11 10:44:04.638 [restartedMain] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-06-11 10:44:04.683 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-11 10:44:04.703 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-11 10:44:04.717 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-11 10:44:04.720 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Started BackendApplication in 0.874 seconds (JVM running for 14.042)
2025-06-11 10:44:04.722 [restartedMain] INFO  o.s.b.d.a.ConditionEvaluationDeltaLoggingListener - Condition evaluation unchanged
2025-06-11 10:44:04.722 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - =====================项目后端启动成功============================
2025-06-11 10:44:15.266 [http-nio-8080-exec-1] INFO  o.a.c.c.ContainerBase.[Tomcat-1].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:44:15.266 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 10:44:15.268 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
2025-06-11 10:44:31.486 [http-nio-8080-exec-16] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[insert]...
2025-06-11 10:46:01.624 [http-nio-8080-exec-37] INFO  org.apache.tomcat.util.http.parser.Cookie - A cookie header was received [1749390907] that contained an invalid cookie. That cookie will be ignored.
 Note: further occurrences of this error will be logged at DEBUG level.
2025-06-11 10:50:43.179 [http-nio-8080-exec-22] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-06-11 10:51:53.534 [http-nio-8080-exec-46] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[insert]...
2025-06-11 10:52:31.971 [http-nio-8080-exec-55] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-06-11 10:52:50.128 [http-nio-8080-exec-68] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-06-11 10:53:18.740 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-11 10:53:18.740 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} closing ...
2025-06-11 10:53:18.743 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-3} closed
2025-06-11 10:53:26.146 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Starting BackendApplication using Java 17.0.1 on LAPTOP-B4MIU7N4 with PID 22348 (D:\jiasu\TianYiyun\by-travel\tourism-backend\target\classes started by 靓仔 in D:\jiasu\TianYiyun\by-travel\tourism-backend)
2025-06-11 10:53:26.148 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - The following profiles are active: dev
2025-06-11 10:53:26.190 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-11 10:53:26.190 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-11 10:53:26.755 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-11 10:53:26.757 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 10:53:26.785 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 17 ms. Found 0 Redis repository interfaces.
2025-06-11 10:53:27.344 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-11 10:53:27.353 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-11 10:53:27.354 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 10:53:27.354 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-11 10:53:27.429 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 10:53:27.429 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1238 ms
2025-06-11 10:53:27.742 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 10:53:28.411 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 10:53:29.056 [restartedMain] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-06-11 10:53:29.268 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-11 10:53:29.301 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-11 10:53:29.334 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-11 10:53:29.342 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Started BackendApplication in 3.652 seconds (JVM running for 4.416)
2025-06-11 10:53:29.345 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - =====================项目后端启动成功============================
2025-06-11 10:53:37.475 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 10:53:37.475 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 10:53:37.476 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-11 10:53:46.714 [http-nio-8080-exec-6] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-06-11 10:53:50.233 [http-nio-8080-exec-8] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-06-11 10:53:59.967 [http-nio-8080-exec-16] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[update]...
2025-06-11 10:56:19.753 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-11 10:56:19.754 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-11 10:56:19.761 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2025-06-11 12:10:33.061 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Starting BackendApplication using Java 17.0.1 on LAPTOP-B4MIU7N4 with PID 23332 (D:\jiasu\TianYiyun\by-travel\tourism-backend\target\classes started by 靓仔 in D:\jiasu\TianYiyun\by-travel\tourism-backend)
2025-06-11 12:10:33.063 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - The following profiles are active: dev
2025-06-11 12:10:33.118 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-06-11 12:10:33.119 [restartedMain] INFO  o.s.b.d.env.DevToolsPropertyDefaultsPostProcessor - For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-06-11 12:10:33.924 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-06-11 12:10:33.926 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-06-11 12:10:33.963 [restartedMain] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 22 ms. Found 0 Redis repository interfaces.
2025-06-11 12:10:34.619 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-06-11 12:10:34.632 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-06-11 12:10:34.633 [restartedMain] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-06-11 12:10:34.633 [restartedMain] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.41]
2025-06-11 12:10:34.715 [restartedMain] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-06-11 12:10:34.716 [restartedMain] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1596 ms
2025-06-11 12:10:35.113 [restartedMain] INFO  c.a.d.s.b.a.DruidDataSourceAutoConfigure - Init DruidDataSource
2025-06-11 12:10:35.858 [restartedMain] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} inited
2025-06-11 12:10:36.564 [restartedMain] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Initializing ExecutorService 'applicationTaskExecutor'
2025-06-11 12:10:36.797 [restartedMain] INFO  o.s.b.d.autoconfigure.OptionalLiveReloadServer - LiveReload server is running on port 35729
2025-06-11 12:10:36.827 [restartedMain] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-06-11 12:10:36.856 [restartedMain] INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path ''
2025-06-11 12:10:36.864 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - Started BackendApplication in 4.423 seconds (JVM running for 5.48)
2025-06-11 12:10:36.868 [restartedMain] INFO  com.shanzhu.tourism.BackendApplication - =====================项目后端启动成功============================
2025-06-11 12:11:25.187 [http-nio-8080-exec-1] INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-11 12:11:25.187 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-06-11 12:11:25.188 [http-nio-8080-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-06-11 12:31:29.579 [http-nio-8080-exec-58] INFO  com.shanzhu.tourism.handle.MyMetaObjectHandler - 公共字段自动填充[insert]...
2025-06-11 12:39:45.727 [SpringContextShutdownHook] INFO  o.s.scheduling.concurrent.ThreadPoolTaskExecutor - Shutting down ExecutorService 'applicationTaskExecutor'
2025-06-11 12:39:45.728 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2025-06-11 12:39:45.732 [SpringContextShutdownHook] INFO  com.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
