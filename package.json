{"name": "learn_front", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"axios": "^1.6.2", "core-js": "^3.8.3", "echarts": "^5.5.0", "element-ui": "^2.15.14", "nprogress": "^0.2.0", "video.js": "^8.6.1", "vue": "^2.6.14", "vue-quill-editor": "^3.0.6", "vue-router": "^3.5.1", "vue-video-player": "^6.0.0", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "^5.0.0", "@vue/cli-service": "^5.0.0", "less": "^4.0.0", "less-loader": "^8.0.0", "sass": "^1.89.1", "sass-loader": "^16.0.5", "vue-template-compiler": "^2.6.14"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}