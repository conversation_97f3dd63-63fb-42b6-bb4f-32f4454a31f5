<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<style lang="scss">
  // 现代化设计系统 - 8px网格 + 完整色彩层级
  :root {
    // === 核心渐变配色系统 ===
    // 主渐变 - 符合设计规范
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);

    // 旅游行业特色渐变
    --ocean-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --nature-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    --sunset-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    --sky-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    --forest-gradient: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);

    // 辅助渐变
    --soft-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --warm-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --cool-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #4a6741 100%);

    // === 完整色彩层级系统 ===
    // 主色调
    --primary-50: #f0f4ff;
    --primary-100: #e0e7ff;
    --primary-200: #c7d2fe;
    --primary-300: #a5b4fc;
    --primary-400: #818cf8;
    --primary-500: #667eea;  // 主色
    --primary-600: #5b21b6;
    --primary-700: #4c1d95;
    --primary-800: #3730a3;
    --primary-900: #312e81;

    // 辅助色调
    --secondary-50: #fdf2f8;
    --secondary-100: #fce7f3;
    --secondary-200: #fbcfe8;
    --secondary-300: #f9a8d4;
    --secondary-400: #f472b6;
    --secondary-500: #f093fb;  // 辅助色
    --secondary-600: #db2777;
    --secondary-700: #be185d;
    --secondary-800: #9d174d;
    --secondary-900: #831843;

    // 旅游行业特色色彩
    --ocean-color: #74b9ff;
    --nature-color: #43e97b;
    --sunset-color: #fa709a;
    --forest-color: #56ab2f;
    --sky-color: #74b9ff;

    // 中性色系统 - 确保对比度符合WCAG 2.1 AA标准
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    // 语义化颜色
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    --info-color: #3b82f6;

    // 背景色系统
    --bg-primary: #f8fafc;
    --bg-secondary: #ffffff;
    --bg-tertiary: #f1f5f9;
    --bg-overlay: rgba(0, 0, 0, 0.5);

    // 文本色系统 - 确保对比度
    --text-primary: #1f2937;      // 对比度 16.94:1
    --text-secondary: #4b5563;    // 对比度 7.07:1
    --text-tertiary: #6b7280;     // 对比度 4.69:1
    --text-disabled: #9ca3af;     // 对比度 2.84:1
    --text-inverse: #ffffff;

    // 边框色系统
    --border-primary: rgba(209, 213, 219, 0.8);
    --border-secondary: rgba(229, 231, 235, 0.6);
    --border-light: rgba(243, 244, 246, 0.4);
    --border-focus: var(--primary-500);

    // === 8px网格间距系统 ===
    --space-0: 0;
    --space-1: 8px;    // 基础单位
    --space-2: 16px;   // 2倍
    --space-3: 24px;   // 3倍
    --space-4: 32px;   // 4倍
    --space-5: 40px;   // 5倍
    --space-6: 48px;   // 6倍
    --space-8: 64px;   // 8倍
    --space-10: 80px;  // 10倍
    --space-12: 96px;  // 12倍
    --space-16: 128px; // 16倍
    --space-20: 160px; // 20倍

    // 兼容性别名
    --spacing-xs: var(--space-1);
    --spacing-sm: var(--space-2);
    --spacing-md: var(--space-3);
    --spacing-lg: var(--space-4);
    --spacing-xl: var(--space-6);
    --spacing-2xl: var(--space-8);

    // === 阴影系统 ===
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-colored: 0 8px 25px rgba(102, 126, 234, 0.3);
    --shadow-colored-lg: 0 20px 40px rgba(102, 126, 234, 0.2);

    // === 毛玻璃效果系统 ===
    --glass-bg-light: rgba(255, 255, 255, 0.1);
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-bg-medium: rgba(255, 255, 255, 0.4);
    --glass-bg-strong: rgba(255, 255, 255, 0.6);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-border-strong: rgba(255, 255, 255, 0.3);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --glass-backdrop: blur(20px);
    --glass-backdrop-strong: blur(40px);

    // === 统一动画系统 ===
    // 动画时长标准
    --duration-fast: 150ms;
    --duration-normal: 300ms;
    --duration-slow: 500ms;
    --duration-slower: 700ms;

    // 缓动函数
    --ease-in: cubic-bezier(0.4, 0, 1, 1);
    --ease-out: cubic-bezier(0, 0, 0.2, 1);
    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);

    // 兼容性别名
    --transition-fast: var(--duration-fast);
    --transition-normal: var(--duration-normal);
    --transition-slow: var(--duration-slow);
    --transition-bounce: var(--ease-bounce);

    // === 字体系统 ===
    --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

    // 字重系统
    --font-weight-thin: 100;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;
    --font-weight-black: 900;

    // 字号系统 - 基于8px网格
    --text-xs: 12px;
    --text-sm: 14px;
    --text-base: 16px;
    --text-lg: 18px;
    --text-xl: 20px;
    --text-2xl: 24px;
    --text-3xl: 30px;
    --text-4xl: 36px;
    --text-5xl: 48px;
    --text-6xl: 60px;

    // 行高系统
    --leading-none: 1;
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    // === 边框半径系统 ===
    --radius-none: 0;
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-2xl: 24px;
    --radius-3xl: 32px;
    --radius-full: 9999px;

    // === Z-index层级系统 ===
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
  }

  // === 全局重置和基础样式 ===
  * {
    box-sizing: border-box;
  }

  *::before,
  *::after {
    box-sizing: border-box;
  }

  html {
    font-size: 16px; // 基础字号
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  html, body, #app, .common-layout {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: var(--font-family-sans);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: var(--leading-normal);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  #app {
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
  }

  // === 无障碍访问支持 ===
  // 减少动画偏好设置
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  // 高对比度模式支持
  @media (prefers-contrast: high) {
    :root {
      --text-primary: #000000;
      --text-secondary: #000000;
      --border-primary: #000000;
      --shadow-sm: none;
      --shadow-md: none;
      --shadow-lg: none;
    }
  }

  // 焦点可见性
  :focus-visible {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
    border-radius: var(--radius-sm);
  }

  // 隐藏默认焦点样式
  :focus:not(:focus-visible) {
    outline: none;
  }

  // 隐藏元素
  .xiaoshi {
    display: none;
  }

  // 屏幕阅读器专用文本
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  // 现代化滚动条样式
  body::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }

  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: transparent;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--primary-gradient);
    border-radius: 10px;
    transition: all var(--transition-normal) ease;

    &:hover {
      background: var(--secondary-gradient);
    }
  }

  // 现代化分页样式
  .el-pagination {
    padding: 20px 0 !important;
    display: flex;
    justify-content: flex-end;
    background: transparent;

    .el-pager li {
      background: var(--glass-bg);
      backdrop-filter: blur(10px);
      border: 1px solid var(--glass-border);
      border-radius: 8px;
      margin: 0 4px;
      transition: all var(--transition-normal) ease;

      &:hover {
        background: var(--primary-gradient);
        color: white;
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);
      }

      &.active {
        background: var(--primary-gradient);
        color: white;
        box-shadow: var(--shadow-md);
      }
    }

    .btn-prev, .btn-next {
      background: var(--glass-bg);
      backdrop-filter: blur(10px);
      border: 1px solid var(--glass-border);
      border-radius: 8px;
      transition: all var(--transition-normal) ease;

      &:hover {
        background: var(--primary-gradient);
        color: white;
        transform: translateY(-2px);
      }
    }
  }

  // 现代化对话框样式
  .el-dialog__wrapper {
    backdrop-filter: blur(8px);
    background: rgba(0, 0, 0, 0.3);

    .el-dialog {
      border-radius: 16px;
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      box-shadow: var(--glass-shadow);
      overflow: hidden;
      animation: dialogSlideIn var(--transition-slow) ease-out;
    }
  }

  .el-dialog__title {
    font-size: 18px !important;
    font-weight: 600;
    color: var(--text-primary);
    font-family: var(--font-family);
  }

  .el-dialog__header {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--glass-border);
    padding: 20px 24px;
  }

  .el-dialog__body {
    padding: 24px;
    background: rgba(255, 255, 255, 0.8);
  }

  // 对话框动画
  @keyframes dialogSlideIn {
    from {
      opacity: 0;
      transform: scale(0.9) translateY(-20px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  // 全局按钮样式增强
  .el-button {
    border-radius: var(--radius-md);
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-normal) ease;
    border: none;
    position: relative;
    overflow: hidden;

    // 光波扩散效果
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: width 0.6s, height 0.6s;
    }

    &:active::before {
      width: 300px;
      height: 300px;
    }

    &.el-button--primary {
      background: var(--primary-gradient);
      box-shadow: var(--shadow-sm);

      &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-colored);
      }
    }

    &.el-button--success {
      background: var(--forest-gradient);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(86, 171, 47, 0.4);
      }
    }

    &.el-button--warning {
      background: var(--sunset-gradient);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(250, 112, 154, 0.4);
      }
    }

    &.el-button--danger {
      background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 65, 108, 0.4);
      }
    }

    &.el-button--info {
      background: var(--sky-gradient);

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(116, 185, 255, 0.4);
      }
    }
  }

  // 现代化卡片样式
  .modern-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal) ease;
    overflow: hidden;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
      transition: left 0.5s;
    }

    &:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-xl);
      border-color: var(--primary-color);

      &::before {
        left: 100%;
      }
    }
  }

  // 输入框样式优化
  .el-input {
    .el-input__inner {
      background: var(--glass-bg);
      border: 1px solid var(--glass-border);
      border-radius: var(--radius-md);
      color: var(--text-primary);
      transition: all var(--transition-normal) ease;

      &:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
        background: var(--bg-secondary);
      }

      &::placeholder {
        color: var(--text-tertiary);
      }
    }
  }

  // === 统一动画系统 ===
  // 基础动画类
  .fade-in {
    animation: fadeIn var(--duration-normal) var(--ease-out);
  }

  .fade-in-up {
    animation: fadeInUp var(--duration-normal) var(--ease-out);
  }

  .fade-in-down {
    animation: fadeInDown var(--duration-normal) var(--ease-out);
  }

  .fade-in-left {
    animation: fadeInLeft var(--duration-normal) var(--ease-out);
  }

  .fade-in-right {
    animation: fadeInRight var(--duration-normal) var(--ease-out);
  }

  .slide-up {
    animation: slideUp var(--duration-normal) var(--ease-out);
  }

  .scale-in {
    animation: scaleIn var(--duration-normal) var(--ease-bounce);
  }

  .bounce-in {
    animation: bounceIn var(--duration-slow) var(--ease-bounce);
  }

  .rotate-in {
    animation: rotateIn var(--duration-normal) var(--ease-out);
  }

  // 延迟动画类
  .delay-100 { animation-delay: 100ms; }
  .delay-200 { animation-delay: 200ms; }
  .delay-300 { animation-delay: 300ms; }
  .delay-500 { animation-delay: 500ms; }

  // 动画关键帧
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(var(--space-4));
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translateY(calc(-1 * var(--space-4)));
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes fadeInLeft {
    from {
      opacity: 0;
      transform: translateX(calc(-1 * var(--space-4)));
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes fadeInRight {
    from {
      opacity: 0;
      transform: translateX(var(--space-4));
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(var(--space-6));
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes bounceIn {
    0% {
      opacity: 0;
      transform: scale(0.3);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes rotateIn {
    from {
      opacity: 0;
      transform: rotate(-200deg);
    }
    to {
      opacity: 1;
      transform: rotate(0);
    }
  }

  // === 工具类系统 ===
  // 间距工具类
  .p-0 { padding: var(--space-0); }
  .p-1 { padding: var(--space-1); }
  .p-2 { padding: var(--space-2); }
  .p-3 { padding: var(--space-3); }
  .p-4 { padding: var(--space-4); }
  .p-6 { padding: var(--space-6); }
  .p-8 { padding: var(--space-8); }

  .m-0 { margin: var(--space-0); }
  .m-1 { margin: var(--space-1); }
  .m-2 { margin: var(--space-2); }
  .m-3 { margin: var(--space-3); }
  .m-4 { margin: var(--space-4); }
  .m-6 { margin: var(--space-6); }
  .m-8 { margin: var(--space-8); }

  // 文本工具类
  .text-center { text-align: center; }
  .text-left { text-align: left; }
  .text-right { text-align: right; }

  .font-light { font-weight: var(--font-weight-light); }
  .font-normal { font-weight: var(--font-weight-normal); }
  .font-medium { font-weight: var(--font-weight-medium); }
  .font-semibold { font-weight: var(--font-weight-semibold); }
  .font-bold { font-weight: var(--font-weight-bold); }

  .text-xs { font-size: var(--text-xs); }
  .text-sm { font-size: var(--text-sm); }
  .text-base { font-size: var(--text-base); }
  .text-lg { font-size: var(--text-lg); }
  .text-xl { font-size: var(--text-xl); }
  .text-2xl { font-size: var(--text-2xl); }

  // 颜色工具类
  .text-primary { color: var(--text-primary); }
  .text-secondary { color: var(--text-secondary); }
  .text-tertiary { color: var(--text-tertiary); }

  // 布局工具类
  .flex { display: flex; }
  .flex-col { flex-direction: column; }
  .flex-row { flex-direction: row; }
  .items-center { align-items: center; }
  .justify-center { justify-content: center; }
  .justify-between { justify-content: space-between; }

  .w-full { width: 100%; }
  .h-full { height: 100%; }

  // 圆角工具类
  .rounded-sm { border-radius: var(--radius-sm); }
  .rounded-md { border-radius: var(--radius-md); }
  .rounded-lg { border-radius: var(--radius-lg); }
  .rounded-xl { border-radius: var(--radius-xl); }
  .rounded-full { border-radius: var(--radius-full); }

  // 响应式设计
  @media (max-width: 768px) {
    .modern-card {
      border-radius: var(--radius-lg);

      &:hover {
        transform: translateY(-2px);
      }
    }

    .el-button {
      &:hover {
        transform: translateY(-1px);
      }
    }
  }


</style>
