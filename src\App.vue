<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<style>
/* === 全局重置和基础样式 === */
html, body, #app, .common-layout {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

/* 隐藏元素工具类 */
.xiaoshi {
  display: none;
}

/* === 现代化滚动条样式 === */
body::-webkit-scrollbar {
  width: 0px;
  height: 0px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  background-color: transparent;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #5a6fd8 0%, #6a4190 100%);
}

/* === Element UI 全局样式优化 === */
/* 分页组件优化 */
.el-pagination {
  padding: 20px 0 !important;
  display: flex;
  justify-content: center;
  align-items: center;
}

.el-pagination .el-pager li {
  border-radius: 6px;
  margin: 0 2px;
  transition: all 0.3s ease;
}

.el-pagination .el-pager li:not(.disabled):hover {
  background-color: #667eea;
  color: white;
  transform: translateY(-1px);
}

.el-pagination .el-pager li.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

/* 对话框现代化样式 */
.el-dialog__wrapper {
  backdrop-filter: blur(8px);
  background-color: rgba(0, 0, 0, 0.3);
}

.el-dialog {
  border-radius: 16px !important;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
}

.el-dialog__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: none;
  padding: 20px 24px;
}

.el-dialog__title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: white !important;
  font-family: inherit;
}

.el-dialog__headerbtn {
  top: 20px;
  right: 20px;
}

.el-dialog__close {
  color: white;
  font-size: 20px;
}

.el-dialog__body {
  padding: 24px;
}

/* 按钮全局优化 */
.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.el-button--primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.el-button--primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.el-button--success {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.el-button--success:hover {
  background: linear-gradient(135deg, #0f8a7f 0%, #32d96a 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(17, 153, 142, 0.4);
}

/* 输入框优化 */
.el-input__inner {
  border-radius: 8px;
  border: 2px solid #e1e5e9;
  transition: all 0.3s ease;
}

.el-input__inner:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* 卡片组件优化 */
.el-card {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.el-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transform: translateY(-4px);
}

/* 消息提示优化 */
.el-message {
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* 加载动画优化 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(4px);
}

/* 空状态优化 */
.el-empty {
  padding: 60px 0;
}

.el-empty__description {
  color: #6b7280;
  font-size: 16px;
}
</style>
