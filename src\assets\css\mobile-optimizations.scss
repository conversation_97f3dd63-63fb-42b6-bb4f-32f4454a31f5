// ===================================
// 移动端优化样式
// ===================================

// === 移动端断点 ===
$mobile-breakpoint: 768px;
$tablet-breakpoint: 1024px;

// === 移动端专用变量 ===
$mobile-header-height: 60px;
$mobile-padding: 16px;
$mobile-card-radius: 12px;
$mobile-button-height: 44px; // 符合触摸标准

// === 触摸友好的交互 ===
@media (max-width: $mobile-breakpoint) {
  
  // 增大可点击区域
  .touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  // 按钮优化
  .el-button {
    min-height: $mobile-button-height;
    padding: 12px 20px;
    font-size: 16px; // 防止iOS缩放
    border-radius: $mobile-card-radius;
    
    &.el-button--small {
      min-height: 36px;
      padding: 8px 16px;
      font-size: 14px;
    }
    
    &.el-button--mini {
      min-height: 32px;
      padding: 6px 12px;
      font-size: 13px;
    }
  }
  
  // 输入框优化
  .el-input__inner {
    height: $mobile-button-height;
    font-size: 16px; // 防止iOS缩放
    padding: 0 16px;
    border-radius: $mobile-card-radius;
  }
  
  // 卡片优化
  .el-card {
    border-radius: $mobile-card-radius;
    margin-bottom: 16px;
    
    .el-card__body {
      padding: $mobile-padding;
    }
  }
  
  // 导航优化
  .global-header-wrapper {
    height: $mobile-header-height;
    padding: 0 $mobile-padding;
    
    .global-header-content {
      padding: 0;
    }
    
    .navigation-menu {
      display: none; // 移动端隐藏主导航，使用抽屉菜单
    }
    
    .logo-area {
      .logo-image {
        height: 40px;
      }
    }
  }
  
  // 主内容区域
  .home-content-wrapper,
  .attractions-main-content {
    width: 100%;
    padding: 0 $mobile-padding;
    margin: 20px auto;
  }
  
  // 网格布局优化
  .item-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  // 搜索面板优化
  .search-panel {
    .el-card__body {
      flex-direction: column;
      gap: 16px;
      
      .search-input {
        width: 100%;
        margin-right: 0;
      }
      
      .search-button {
        width: 100%;
      }
    }
  }
  
  // 分页优化
  .el-pagination {
    justify-content: center;
    flex-wrap: wrap;
    
    .el-pager {
      li {
        min-width: 36px;
        height: 36px;
        line-height: 36px;
        margin: 0 2px;
      }
    }
    
    .btn-prev,
    .btn-next {
      min-width: 36px;
      height: 36px;
    }
  }
  
  // 表单优化
  .el-form {
    .el-form-item {
      margin-bottom: 20px;
      
      .el-form-item__label {
        font-size: 16px;
        line-height: 1.4;
        margin-bottom: 8px;
      }
    }
  }
  
  // 对话框优化
  .el-dialog {
    width: 95% !important;
    margin: 20px auto !important;
    
    .el-dialog__header {
      padding: 16px 20px;
    }
    
    .el-dialog__body {
      padding: 20px;
      max-height: 60vh;
      overflow-y: auto;
    }
    
    .el-dialog__footer {
      padding: 16px 20px;
      
      .el-button {
        width: 100%;
        margin: 4px 0;
        
        &:first-child {
          margin-bottom: 8px;
        }
      }
    }
  }
  
  // 表格优化
  .el-table {
    font-size: 14px;
    
    .el-table__header th {
      padding: 8px 0;
      font-size: 13px;
    }
    
    .el-table__body td {
      padding: 12px 8px;
    }
  }
  
  // 标签页优化
  .el-tabs {
    .el-tabs__header {
      .el-tabs__nav-scroll {
        .el-tabs__nav {
          .el-tabs__item {
            height: 44px;
            line-height: 44px;
            padding: 0 16px;
            font-size: 15px;
          }
        }
      }
    }
  }
  
  // 步骤条优化
  .el-steps {
    .el-step {
      .el-step__head {
        .el-step__icon {
          width: 32px;
          height: 32px;
          line-height: 32px;
          font-size: 14px;
        }
      }
      
      .el-step__title {
        font-size: 14px;
        line-height: 1.4;
      }
      
      .el-step__description {
        font-size: 12px;
      }
    }
  }
}

// === 横屏优化 ===
@media (max-width: $mobile-breakpoint) and (orientation: landscape) {
  .global-header-wrapper {
    height: 50px;
  }
  
  .home-carousel {
    height: 300px;
  }
  
  .el-dialog {
    .el-dialog__body {
      max-height: 40vh;
    }
  }
}

// === 超小屏幕优化 ===
@media (max-width: 480px) {
  .home-content-wrapper,
  .attractions-main-content {
    padding: 0 12px;
  }
  
  .content-section {
    padding: 16px 12px;
    margin-bottom: 16px;
    
    .section-title {
      font-size: 18px;
      margin-bottom: 16px;
    }
  }
  
  .item-card {
    .item-info {
      padding: 12px;
    }
    
    .item-name {
      font-size: 15px;
    }
    
    .item-introduce {
      font-size: 13px;
    }
  }
  
  .visitor-stats-banner {
    padding: 20px 12px;
    font-size: 14px;
    
    .visitor-count {
      font-size: 20px;
      display: block;
      margin: 8px 0 0 0;
    }
  }
}

// === 可访问性增强 ===
@media (max-width: $mobile-breakpoint) {
  
  // 增强焦点样式
  .el-button:focus,
  .el-input__inner:focus,
  .el-textarea__inner:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
  }
  
  // 增大文字间距
  body {
    line-height: 1.6;
    letter-spacing: 0.3px;
  }
  
  // 增强对比度
  .text-secondary {
    color: #4b5563; // 提高对比度
  }
  
  // 减少动画（尊重用户偏好）
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// === 性能优化 ===
@media (max-width: $mobile-breakpoint) {
  
  // 减少阴影复杂度
  .el-card,
  .card-modern {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
  
  // 简化渐变
  .visitor-stats-banner {
    background: #667eea; // 移动端使用纯色背景
  }
  
  // 减少模糊效果
  .global-header-wrapper {
    backdrop-filter: none;
    background: rgba(255, 255, 255, 0.95);
  }
}

// === 手势支持 ===
@media (max-width: $mobile-breakpoint) {
  
  // 轮播图手势
  .home-carousel {
    .el-carousel__container {
      touch-action: pan-y pinch-zoom;
    }
  }
  
  // 可滑动的标签页
  .el-tabs__nav-scroll {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }
  
  // 可滑动的表格
  .el-table__body-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

// === 安全区域适配（iPhone X等） ===
@supports (padding: max(0px)) {
  @media (max-width: $mobile-breakpoint) {
    .global-header-wrapper {
      padding-left: max(16px, env(safe-area-inset-left));
      padding-right: max(16px, env(safe-area-inset-right));
    }
    
    .app-footer {
      padding-bottom: max(40px, env(safe-area-inset-bottom));
    }
    
    .home-content-wrapper,
    .attractions-main-content {
      padding-left: max(16px, env(safe-area-inset-left));
      padding-right: max(16px, env(safe-area-inset-right));
    }
  }
}
