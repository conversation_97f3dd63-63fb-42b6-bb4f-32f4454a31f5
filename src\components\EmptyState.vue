<template>
  <div class="empty-state-wrapper" :class="[`size-${size}`, { 'with-action': hasAction }]">
    <div class="empty-content">
      <!-- 图标或插图 -->
      <div class="empty-icon" v-if="!$slots.icon">
        <i :class="iconClass" v-if="iconClass"></i>
        <div v-else class="default-illustration">
          <svg viewBox="0 0 200 200" class="illustration-svg">
            <defs>
              <linearGradient id="emptyGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#667eea;stop-opacity:0.3" />
                <stop offset="100%" style="stop-color:#764ba2;stop-opacity:0.1" />
              </linearGradient>
            </defs>
            <circle cx="100" cy="100" r="80" fill="url(#emptyGradient)" />
            <path d="M70 90 Q100 70 130 90 Q130 120 100 130 Q70 120 70 90" fill="#667eea" opacity="0.2"/>
            <circle cx="85" cy="95" r="3" fill="#667eea" opacity="0.6"/>
            <circle cx="115" cy="95" r="3" fill="#667eea" opacity="0.6"/>
            <path d="M85 115 Q100 125 115 115" stroke="#667eea" stroke-width="2" fill="none" opacity="0.4"/>
          </svg>
        </div>
      </div>
      <slot name="icon" v-else></slot>

      <!-- 标题 -->
      <h3 class="empty-title" v-if="title">{{ title }}</h3>

      <!-- 描述 -->
      <p class="empty-description" v-if="description">{{ description }}</p>

      <!-- 操作按钮 -->
      <div class="empty-actions" v-if="hasAction">
        <slot name="action">
          <el-button 
            v-if="actionText" 
            :type="actionType" 
            :size="actionSize"
            @click="handleAction"
            class="action-button"
          >
            <i :class="actionIcon" v-if="actionIcon"></i>
            {{ actionText }}
          </el-button>
        </slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmptyState',
  props: {
    // 标题
    title: {
      type: String,
      default: '暂无数据'
    },
    // 描述
    description: {
      type: String,
      default: ''
    },
    // 图标类名
    iconClass: {
      type: String,
      default: ''
    },
    // 操作按钮文本
    actionText: {
      type: String,
      default: ''
    },
    // 操作按钮类型
    actionType: {
      type: String,
      default: 'primary'
    },
    // 操作按钮大小
    actionSize: {
      type: String,
      default: 'medium'
    },
    // 操作按钮图标
    actionIcon: {
      type: String,
      default: ''
    },
    // 组件大小
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    }
  },
  computed: {
    hasAction() {
      return this.actionText || this.$slots.action;
    }
  },
  methods: {
    handleAction() {
      this.$emit('action');
    }
  }
}
</script>

<style scoped lang="scss">
// === 空状态组件现代化设计 ===
.empty-state-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #6b7280;
  
  // 尺寸变体
  &.size-small {
    padding: 30px 20px;
    
    .empty-icon {
      width: 80px;
      height: 80px;
      margin-bottom: 16px;
    }
    
    .empty-title {
      font-size: 16px;
      margin-bottom: 8px;
    }
    
    .empty-description {
      font-size: 13px;
    }
  }
  
  &.size-medium {
    padding: 60px 40px;
    
    .empty-icon {
      width: 120px;
      height: 120px;
      margin-bottom: 24px;
    }
    
    .empty-title {
      font-size: 20px;
      margin-bottom: 12px;
    }
    
    .empty-description {
      font-size: 15px;
    }
  }
  
  &.size-large {
    padding: 80px 60px;
    
    .empty-icon {
      width: 160px;
      height: 160px;
      margin-bottom: 32px;
    }
    
    .empty-title {
      font-size: 24px;
      margin-bottom: 16px;
    }
    
    .empty-description {
      font-size: 16px;
    }
  }
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

// === 图标样式 ===
.empty-icon {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0.8;
  transition: all 0.3s ease;
  
  i {
    font-size: 4em;
    color: #9ca3af;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.default-illustration {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  
  .illustration-svg {
    width: 100%;
    height: 100%;
    max-width: 200px;
    max-height: 200px;
    animation: float 6s ease-in-out infinite;
  }
}

// === 标题样式 ===
.empty-title {
  color: #374151;
  font-weight: 600;
  margin: 0;
  line-height: 1.4;
}

// === 描述样式 ===
.empty-description {
  color: #6b7280;
  line-height: 1.6;
  margin: 0 0 24px 0;
  max-width: 300px;
  margin-left: auto;
  margin-right: auto;
}

// === 操作按钮样式 ===
.empty-actions {
  margin-top: 24px;
  
  .action-button {
    border-radius: 12px;
    padding: 12px 32px;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;
      
      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
      }
    }
    
    i {
      margin-right: 8px;
    }
  }
}

// === 动画效果 ===
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.empty-state-wrapper:hover {
  .empty-icon {
    transform: scale(1.05);
  }
  
  .default-illustration .illustration-svg {
    animation-duration: 3s;
  }
}

// === 响应式设计 ===
@media (max-width: 768px) {
  .empty-state-wrapper {
    &.size-small {
      padding: 20px 15px;
    }
    
    &.size-medium {
      padding: 40px 20px;
    }
    
    &.size-large {
      padding: 50px 30px;
    }
  }
  
  .empty-content {
    max-width: 300px;
  }
  
  .empty-description {
    max-width: 250px;
  }
  
  .action-button {
    width: 100%;
    max-width: 200px;
  }
}

// === 主题变体 ===
.empty-state-wrapper.theme-success {
  .empty-icon i {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.empty-state-wrapper.theme-warning {
  .empty-icon i {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

.empty-state-wrapper.theme-info {
  .empty-icon i {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}
</style>
