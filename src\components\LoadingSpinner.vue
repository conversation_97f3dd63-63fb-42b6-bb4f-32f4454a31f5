<template>
  <div class="loading-spinner-wrapper" :class="{ 'full-screen': fullScreen }">
    <div class="loading-content">
      <div class="spinner-container">
        <div class="spinner" :class="spinnerType">
          <div v-if="spinnerType === 'dots'" class="dot" v-for="i in 3" :key="i"></div>
          <div v-else-if="spinnerType === 'pulse'" class="pulse-ring" v-for="i in 3" :key="i"></div>
          <div v-else-if="spinnerType === 'wave'" class="wave-bar" v-for="i in 5" :key="i"></div>
        </div>
      </div>
      <p v-if="text" class="loading-text">{{ text }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LoadingSpinner',
  props: {
    // 加载文本
    text: {
      type: String,
      default: '加载中...'
    },
    // 是否全屏显示
    fullScreen: {
      type: Boolean,
      default: false
    },
    // 加载动画类型
    spinnerType: {
      type: String,
      default: 'dots',
      validator: value => ['dots', 'pulse', 'wave', 'circle'].includes(value)
    },
    // 大小
    size: {
      type: String,
      default: 'medium',
      validator: value => ['small', 'medium', 'large'].includes(value)
    }
  }
}
</script>

<style scoped lang="scss">
// === 加载组件现代化设计 ===
.loading-spinner-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  
  &.full-screen {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(8px);
    z-index: 9999;
  }
}

.loading-content {
  text-align: center;
}

.spinner-container {
  margin-bottom: 16px;
}

.loading-text {
  color: #6b7280;
  font-size: 16px;
  font-weight: 500;
  margin: 0;
  animation: pulse-text 2s ease-in-out infinite;
}

// === 点状加载动画 ===
.spinner.dots {
  display: flex;
  gap: 8px;
  justify-content: center;
  
  .dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    animation: dot-bounce 1.4s ease-in-out infinite both;
    
    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
    &:nth-child(3) { animation-delay: 0s; }
  }
}

@keyframes dot-bounce {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

// === 脉冲环形加载动画 ===
.spinner.pulse {
  position: relative;
  width: 60px;
  height: 60px;
  margin: 0 auto;
  
  .pulse-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 3px solid transparent;
    border-top-color: #667eea;
    border-radius: 50%;
    animation: pulse-ring 2s linear infinite;
    
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.7s; }
    &:nth-child(3) { animation-delay: 1.4s; }
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

// === 波浪加载动画 ===
.spinner.wave {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: flex-end;
  height: 40px;
  
  .wave-bar {
    width: 6px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
    animation: wave-bounce 1.2s ease-in-out infinite;
    
    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.1s; }
    &:nth-child(3) { animation-delay: 0.2s; }
    &:nth-child(4) { animation-delay: 0.3s; }
    &:nth-child(5) { animation-delay: 0.4s; }
  }
}

@keyframes wave-bounce {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

// === 圆形旋转加载动画 ===
.spinner.circle {
  width: 50px;
  height: 50px;
  margin: 0 auto;
  border: 4px solid #e5e7eb;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: circle-spin 1s linear infinite;
}

@keyframes circle-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// === 文字脉冲动画 ===
@keyframes pulse-text {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.6; }
}

// === 响应式设计 ===
@media (max-width: 768px) {
  .loading-spinner-wrapper {
    padding: 30px;
  }
  
  .loading-text {
    font-size: 14px;
  }
  
  .spinner.dots .dot {
    width: 10px;
    height: 10px;
  }
  
  .spinner.pulse {
    width: 50px;
    height: 50px;
  }
  
  .spinner.wave {
    height: 35px;
    
    .wave-bar {
      width: 5px;
      height: 18px;
    }
  }
  
  .spinner.circle {
    width: 40px;
    height: 40px;
    border-width: 3px;
  }
}
</style>
