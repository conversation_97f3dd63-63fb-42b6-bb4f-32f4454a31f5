<template>
  <footer class="app-footer"> 
    <div class="footer-content-wrapper">
      <div class="footer-section footer-logo-area">
        <img src="../assets/image/Frame 28.png" alt="网站推广图或Logo" class="footer-image" />
      </div>

      <div class="footer-section footer-intro-area">
        <h4 class="footer-section-title">网站简介</h4>
        <p class="intro-text"> 
          系统角色上分为管理员以及普通用户进行实现， 管理员主要负责整个网站后台的维护管理，例如包括用户管理、景点以及分类管理、旅游路线管理、酒店管理、资讯管理、轮播图管理、个人中心等功能； 前台包括用户登陆注册、忘记密码、旅游景点查询和预定、旅游路线查询和收藏、酒店查询和预订、资讯、我的收藏、我的预定、个人中心等功能。
        </p>
      </div>

      <div class="footer-section footer-contact-area">
        <h4 class="footer-section-title">联系我们</h4>
        <ul class="contact-list"> 
          <li><i class="el-icon-message"></i> 邮箱: <EMAIL></li>
          <li><i class="el-icon-chat-dot-round"></i> QQ: xxx</li> 
          <li><i class="el-icon-chat-line-round"></i> 微信: xxx</li>
          <li><i class="el-icon-position"></i> 微博: <EMAIL></li>
          <li><i class="el-icon-phone-outline"></i> 电话: xxx</li>
        </ul>
        
        </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'GlobalFooter', // 1. 组件命名
  data() {
    return {
      // 如果底部组件确实不需要响应式数据，可以保持为空
    };
  },
  methods: {
    // toPage 方法在此组件中似乎未使用，如果确实不需要，可以移除
    // 如果底部有链接需要跳转，可以保留或改用 router-link
  },
};
</script>

<style scoped lang="scss">
// === 现代化底部设计变量 ===
$footer-bg: linear-gradient(135deg, #1f2937 0%, #111827 100%);
$footer-text-color: #d1d5db;
$footer-title-color: #ffffff;
$footer-link-hover-color: #667eea;
$footer-border-color: #374151;
$footer-accent-color: #667eea;

$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
$footer-content-width: 90%;
$footer-max-width: 1400px;

// === 底部容器现代化设计 ===
.app-footer {
  background: $footer-bg;
  color: $footer-text-color;
  padding: 60px 0 40px;
  font-family: $font-family-base;
  font-size: 14px;
  line-height: 1.8;
  margin-top: 60px;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;

  // 添加装饰性背景图案
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
  }

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 40%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.03) 0%, transparent 70%);
    animation: float 20s ease-in-out infinite;
  }
}

.footer-content-wrapper {
  width: $footer-content-width;
  max-width: $footer-max-width;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 40px;
  position: relative;
  z-index: 1;

  @media (max-width: 992px) {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 40px;
  }

  @media (max-width: 768px) {
    width: 95%;
    gap: 32px;
    padding: 0 20px;
  }
}

// === 各区域现代化样式 ===
.footer-section {
  flex: 1;
  min-width: 280px;

  &.footer-logo-area {
    flex: 0 1 auto;
    max-width: 300px;
    text-align: center;

    @media (max-width: 992px) {
      margin-bottom: 20px;
    }
  }

  &.footer-intro-area {
    flex: 2;

    @media (max-width: 992px) {
      order: 2;
      margin-top: 20px;
    }
  }

  &.footer-contact-area {
    @media (max-width: 992px) {
      order: 1;
    }
  }
}

.footer-image {
  max-width: 100%;
  height: auto;
  max-height: 140px;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
  }
}

.footer-section-title {
  font-size: 20px;
  color: $footer-title-color;
  font-weight: 600;
  margin: 0 0 20px 0;
  padding-bottom: 12px;
  position: relative;

  // 现代化标题装饰
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(90deg, $footer-accent-color 0%, transparent 100%);
    border-radius: 2px;
  }

  @media (max-width: 992px) {
    &::after {
      left: 50%;
      transform: translateX(-50%);
    }
  }
}

.intro-text {
  letter-spacing: 0.3px;
  text-align: justify;
  opacity: 0.9;
  line-height: 1.7;
}

.contact-list {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    padding: 8px 0;
    transition: all 0.3s ease;
    border-radius: 8px;

    &:hover {
      background: rgba(102, 126, 234, 0.1);
      padding-left: 12px;
      transform: translateX(4px);
    }

    i {
      margin-right: 12px;
      font-size: 18px;
      color: $footer-accent-color;
      width: 24px;
      text-align: center;
      transition: transform 0.3s ease;
    }

    &:hover i {
      transform: scale(1.1);
    }

    a {
      color: $footer-text-color;
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        color: $footer-link-hover-color;
      }
    }
  }

  @media (max-width: 992px) {
    align-items: center;

    li {
      justify-content: center;

      &:hover {
        padding-left: 8px;
        transform: none;
      }
    }
  }
}

.footer-copyright { // 版权信息（如果添加）
  margin-top: 25px;
  padding-top: 15px;
  border-top: 1px solid $footer-border-color;
  text-align: center;
  font-size: 12px;
  color: #e5e7eb;
  width: 100%; // 在flex布局中确保它能横跨

  a {
    color: #f3f4f6;
    &:hover { color: $footer-link-hover-color; }
  }
}

// === 动画效果 ===
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-10px) rotate(180deg);
  }
}
</style>