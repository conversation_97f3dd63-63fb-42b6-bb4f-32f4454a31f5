<template>
  <div class="global-header-wrapper">
    <div class="global-header-content">
      <div class="logo-area" @click="navigateTo('/')" title="返回首页">
        <img class="logo-image" src="../assets/image/lv2.jpg" alt="系统Logo" />
        </div>

      <nav class="navigation-menu">
        <el-menu
          :default-active="activeMenu"
          class="el-menu-nav"
          mode="horizontal"
          @select="handleMenuSelect"
          active-text-color="#DC1616" 
          text-color="#303133"
        >
          <el-menu-item index="/">平台主页</el-menu-item>
          <el-menu-item index="/attractions">景点信息</el-menu-item>
          <el-menu-item index="/line">旅游线路</el-menu-item>
          <el-menu-item index="/hotel">景区酒店</el-menu-item>
          <el-menu-item index="/forum">旅游资讯</el-menu-item>
          <el-menu-item v-if="isUserLoggedIn" index="/favor">我的收藏</el-menu-item>
          <el-menu-item v-if="isUserLoggedIn" index="/order">我的预订</el-menu-item>
          <el-menu-item v-if="canAccessManage" index="manageExternal">后台管理</el-menu-item>
        </el-menu>
      </nav>

      <div class="user-actions-area">
        <template v-if="isUserLoggedIn">
          <el-dropdown @command="handleUserCommand" trigger="click">
            <span class="el-dropdown-link user-avatar-trigger">
              <el-avatar :size="36" :src="userAvatarUrl" icon="el-icon-user-solid" class="user-avatar-img"></el-avatar>
              <span class="user-name">{{ userNameDisplay }}</span>
              <i class="el-icon-arrow-down el-icon--right"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="center" icon="el-icon-user">个人中心</el-dropdown-item>
              <el-dropdown-item command="logout" icon="el-icon-switch-button" divided>退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
        <template v-else>
          <el-button type="text" @click="navigateTo('/login')" class="auth-button">登录</el-button>
          <el-button type="text" @click="navigateTo('/register')" class="auth-button">注册</el-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
// 假设您有Vuex store来管理用户信息和登录状态
import { mapState, mapActions } from 'vuex'; // 如果使用Vuex

export default {
  name: 'GlobalHeader',
  data() {
    return {
      // activeMenu: this.$route.path, // el-menu的激活状态可以由路由路径决定
      userInfoUpdateTime: Date.now() // 用于强制更新用户信息
    };
  },
  computed: {
    // ...mapState('user', ['userInfo', 'token']), // 假设您的用户模块名为'user'
    
    // 示例：从localStorage或Vuex获取用户信息
    currentUser() {
      const userInfoStr = window.localStorage.getItem("user_info");
      if (userInfoStr) {
        try {
          // 添加一个响应式的时间戳来强制更新
          this.userInfoUpdateTime; // 触发响应式更新
          return JSON.parse(userInfoStr);
        } catch (e) { return null; }
      }
      return null;
    },
    isUserLoggedIn() {
      // 检查token和用户信息是否都存在
      const hasToken = !!window.localStorage.getItem("user_token");
      const hasUserInfo = !!this.currentUser;

      // 如果没有token，肯定是未登录状态
      if (!hasToken) {
        return false;
      }

      // 如果有token但没有用户信息，也认为是未登录状态
      return hasToken && hasUserInfo;
    },
    userAvatarUrl() {
      if (!this.currentUser || !this.currentUser.avatar) {
        return require('../assets/image/404.png'); // 使用默认头像
      }

      const avatar = this.currentUser.avatar;

      // 如果头像路径已经是完整的HTTP URL，直接返回
      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
        return avatar;
      }

      // 如果头像路径是相对路径（以/开头），拼接HOST
      if (avatar.startsWith('/')) {
        return (this.$store.state.HOST || '') + avatar;
      }

      // 其他情况，也拼接HOST（兼容不以/开头的相对路径）
      return (this.$store.state.HOST || '') + '/' + avatar;
    },
    userNameDisplay() {
      return this.currentUser ? this.currentUser.userName || this.currentUser.loginAccount || '用户' : '游客';
    },
    activeMenu() { // 用于el-menu的激活状态
        // 对于 "后台管理"，它不是一个前端路由，所以特殊处理
        if (this.$route.path.startsWith('/manage-placeholder')) { // 假设为后台管理创建了一个占位路由
            return 'manageExternal';
        }
        return this.$route.path === '/' ? '/' : this.$route.path.toLowerCase();
    },
    canAccessManage() {
      // 实际项目中应根据用户角色判断是否显示后台管理入口
      // return this.currentUser && (this.currentUser.role === 'admin' || this.currentUser.role === 'editor');
      return true; // 示例：暂时都显示
    }
  },
  methods: {
    // ...mapActions('user', ['logoutAction']), // 假设Vuex中有 logoutAction

    navigateTo(path) {
      if (this.$route.path !== path) {
        this.$router.push(path);
      }
    },
    handleMenuSelect(index, indexPath) {
      if (index === 'manageExternal') {
        window.open("http://localhost:3000", "_blank"); // 后台管理在新标签页打开
      } else if (index) {
        this.navigateTo(index);
      }
    },
    handleUserCommand(command) {
      if (command === 'center') {
        this.navigateTo('/center');
      } else if (command === 'logout') {
        this.handleLogout();
      }
    },
    async handleLogout() { // 使用 async/await
      try {
        // 如果有Vuex action: await this.logoutAction(); 
        // 否则直接执行清理逻辑：
        this.$store.commit('SET_TOKEN', ''); // 假设有Vuex mutation清空token
        this.$store.commit('SET_USER_INFO', null); // 假设有Vuex mutation清空用户信息
        window.localStorage.removeItem("user_info");
        window.localStorage.removeItem("user_token");
        
        this.$message.success('退出成功');
        
        // 退出后跳转到登录页或首页
        if (this.$route.path !== '/') {
          this.$router.push('/'); // 跳转到首页，避免停留在需要登录的页面
        }
        // 强制刷新通常不是最佳实践，但如果必须，可以保留
        // setTimeout(() => { window.location.reload(); }, 500);
      } catch (error) {
        console.error("退出登录失败:", error);
        this.$message.error('退出操作失败');
      }
    },



    // 刷新用户信息
    refreshUserInfo() {
      this.userInfoUpdateTime = Date.now();
      this.$forceUpdate(); // 强制更新组件
    }
  },

  mounted() {
    // 监听自定义事件来刷新用户信息
    this.$bus && this.$bus.$on('refreshUserInfo', this.refreshUserInfo);
    this.$bus && this.$bus.$on('avatarUpdated', this.refreshUserInfo);
    this.$bus && this.$bus.$on('userInfoUpdated', this.refreshUserInfo);

    // 监听localStorage变化（仅在同一页面内有效）
    window.addEventListener('storage', (e) => {
      if (e.key === 'user_info' || e.key === 'user_token') {
        this.refreshUserInfo();
      }
    });

    // 添加自定义的localStorage监听（用于同一页面内的变化）
    this.originalSetItem = localStorage.setItem;
    this.originalRemoveItem = localStorage.removeItem;
    this.originalClear = localStorage.clear;

    const self = this;
    localStorage.setItem = function(key, value) {
      self.originalSetItem.apply(this, arguments);
      if (key === 'user_info' || key === 'user_token') {
        self.refreshUserInfo();
      }
    };

    localStorage.removeItem = function(key) {
      self.originalRemoveItem.apply(this, arguments);
      if (key === 'user_info' || key === 'user_token') {
        self.refreshUserInfo();
      }
    };

    localStorage.clear = function() {
      self.originalClear.apply(this, arguments);
      self.refreshUserInfo();
    };
  },

  beforeDestroy() {
    // 清理事件监听
    this.$bus && this.$bus.$off('refreshUserInfo', this.refreshUserInfo);
    this.$bus && this.$bus.$off('avatarUpdated', this.refreshUserInfo);
    this.$bus && this.$bus.$off('userInfoUpdated', this.refreshUserInfo);
    window.removeEventListener('storage', this.refreshUserInfo);

    // 恢复原始的localStorage方法
    if (this.originalSetItem) {
      localStorage.setItem = this.originalSetItem;
    }
    if (this.originalRemoveItem) {
      localStorage.removeItem = this.originalRemoveItem;
    }
    if (this.originalClear) {
      localStorage.clear = this.originalClear;
    }
  },
  // watch: { // 监听路由变化更新激活菜单 (el-menu :default-active 会自动处理)
  //   '$route.path'(newPath) {
  //     this.activeMenu = newPath;
  //   }
  // },
};
</script>

<style scoped lang="scss">
// === 现代化设计变量 ===
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$header-bg: rgba(255, 255, 255, 0.95);
$header-height: 80px;
$header-content-width: 90%;
$header-max-width: 1400px;

$text-color-primary: #1f2937;
$text-color-regular: #4b5563;
$text-color-secondary: #6b7280;
$active-menu-color: #667eea;

$border-color-light: #e5e7eb;
$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;

// === 头部容器现代化设计 ===
.global-header-wrapper {
  width: 100%;
  height: $header-height;
  background: $header-bg;
  backdrop-filter: blur(20px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  display: flex;
  justify-content: center;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
}

.global-header-content {
  width: $header-content-width;
  max-width: $header-max-width;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

// === Logo区域现代化设计 ===
.logo-area {
  display: flex;
  align-items: center;
  cursor: pointer;
  height: 100%;
  padding: 8px 0;
  box-sizing: border-box;
  transition: all 0.3s ease;
  border-radius: 12px;
  padding: 8px 16px;

  &:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.02);
  }

  .logo-image {
    height: 100%;
    width: auto;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    transition: filter 0.3s ease;
  }

  .logo-title {
    font-size: 24px;
    font-weight: 700;
    background: $primary-gradient;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-left: 12px;
    letter-spacing: -0.5px;
  }
}

// === 导航菜单现代化设计 ===
.navigation-menu {
  flex-grow: 1;
  display: flex;
  justify-content: center;

  .el-menu-nav {
    border-bottom: none !important;
    background-color: transparent;
    height: $header-height;

    .el-menu-item {
      font-size: 16px;
      font-family: $font-family-base;
      font-weight: 500;
      height: $header-height;
      line-height: $header-height;
      padding: 0 24px;
      margin: 0 4px;
      border-radius: 12px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      border-bottom-color: transparent !important;
      position: relative;

      // 添加悬浮指示器
      &::before {
        content: '';
        position: absolute;
        bottom: 8px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 3px;
        background: $primary-gradient;
        border-radius: 2px;
        transition: width 0.3s ease;
      }

      &:hover {
        background: rgba(102, 126, 234, 0.08) !important;
        color: $active-menu-color !important;
        transform: translateY(-1px);

        &::before {
          width: 24px;
        }
      }

      &.is-active {
        font-weight: 600;
        color: $active-menu-color !important;
        background: rgba(102, 126, 234, 0.1) !important;

        &::before {
          width: 32px;
        }
      }
    }
  }
}

// === 用户操作区域现代化设计 ===
.user-actions-area {
  display: flex;
  align-items: center;
  gap: 16px;

  .user-avatar-trigger {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 16px;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(102, 126, 234, 0.1);

    &:hover {
      background: rgba(102, 126, 234, 0.05);
      border-color: rgba(102, 126, 234, 0.2);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
    }

    .user-avatar-img {
      margin-right: 12px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      border: 2px solid white;
      transition: transform 0.3s ease;
    }

    &:hover .user-avatar-img {
      transform: scale(1.05);
    }

    .user-name {
      font-size: 15px;
      font-weight: 500;
      color: $text-color-primary;
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .el-icon--right {
      margin-left: 8px;
      transition: transform 0.3s ease;
      color: $text-color-secondary;
    }

    &[aria-expanded="true"] .el-icon--right {
      transform: rotate(180deg);
    }
  }

  .auth-button {
    font-size: 15px;
    font-weight: 500;
    color: $text-color-regular;
    padding: 10px 20px;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid transparent;

    &:first-child {
      color: $active-menu-color;
      border-color: $active-menu-color;

      &:hover {
        background: $primary-gradient;
        color: white;
        border-color: transparent;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }
    }

    &:last-child {
      background: $primary-gradient;
      color: white;

      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }
    }
  }
}

// 旧的菜单样式 (如果不用 el-menu，可以参考这个进行美化)
/*
.menu-list {
  display: flex;
  align-items: center; // 垂直居中
  height: 100%;
}
.menu-list > div {
  font-family: $font-family-base;
  font-size: 15px; // 调整字体大小
  padding: 0 18px; // 左右内边距
  height: 100%;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: $text-color-primary;
  border-bottom: 2px solid transparent; // 预留底部边框空间
  transition: color 0.2s ease, border-color 0.2s ease;

  &:hover {
    color: $active-menu-color;
  }
  &.menu_item_active { // 使用更规范的激活类名
    color: $active-menu-color;
    border-bottom-color: $active-menu-color;
    font-weight: 500;
  }
}
*/
</style>