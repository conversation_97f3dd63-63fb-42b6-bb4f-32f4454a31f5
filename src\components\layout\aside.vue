<template>
  <el-aside :width="isCollapse ? '80px' : '280px'" class="modern-aside">
    <!-- Logo区域 -->
    <div class="logo-container" :class="{ collapsed: isCollapse }">
      <div class="logo-wrapper">
        <img class="logo-image" src="../../assets/image/logo1.png" alt="Logo"/>
        <div class="logo-text" v-show="!isCollapse">
          <h3>旅游推荐平台</h3>
          <span>Tourism Admin</span>
        </div>
      </div>
    </div>

    <!-- 导航菜单 -->
    <el-menu
      @select="handleSelect"
      :collapse="isCollapse"
      :collapse-transition="true"
      class="modern-menu"
      :unique-opened="true"
      :default-active="this.$route.path + '@' + this.$route.name"
    >
      <!-- 首页 -->
      <el-menu-item index="/index@首页" class="menu-item-modern">
        <div class="menu-item-content">
          <i class="menu-icon el-icon-s-home"></i>
          <span class="menu-title">首页</span>
        </div>
      </el-menu-item>

      <!-- 景点管理 -->
      <el-menu-item index="/attractions@景点管理" class="menu-item-modern">
        <div class="menu-item-content">
          <i class="menu-icon el-icon-place"></i>
          <span class="menu-title">景点管理</span>
        </div>
      </el-menu-item>

      <!-- 线路管理 -->
      <el-menu-item index="/line@线路管理" class="menu-item-modern">
        <div class="menu-item-content">
          <i class="menu-icon el-icon-guide"></i>
          <span class="menu-title">线路管理</span>
        </div>
      </el-menu-item>

      <!-- 酒店管理 -->
      <el-menu-item index="/hotel@酒店管理" class="menu-item-modern">
        <div class="menu-item-content">
          <i class="menu-icon el-icon-office-building"></i>
          <span class="menu-title">酒店管理</span>
        </div>
      </el-menu-item>

      <!-- 资讯管理 -->
      <el-menu-item index="/forum@资讯管理" class="menu-item-modern">
        <div class="menu-item-content">
          <i class="menu-icon el-icon-document"></i>
          <span class="menu-title">资讯管理</span>
        </div>
      </el-menu-item>

      <!-- 预约管理子菜单 -->
      <el-submenu index="booking" class="submenu-modern">
        <template slot="title">
          <div class="menu-item-content">
            <i class="menu-icon el-icon-date"></i>
            <span class="menu-title">预约管理</span>
          </div>
        </template>
        <el-menu-item index="/order@景点预约" class="submenu-item-modern">
          <div class="submenu-item-content">
            <i class="submenu-icon el-icon-tickets"></i>
            <span class="submenu-title">景点预约</span>
          </div>
        </el-menu-item>
        <el-menu-item index="/orderHotel@酒店预约" class="submenu-item-modern">
          <div class="submenu-item-content">
            <i class="submenu-icon el-icon-s-home"></i>
            <span class="submenu-title">酒店预约</span>
          </div>
        </el-menu-item>
      </el-submenu>

      <!-- 轮播图管理 -->
      <el-menu-item index="/rotations@轮播图管理" class="menu-item-modern">
        <div class="menu-item-content">
          <i class="menu-icon el-icon-picture-outline"></i>
          <span class="menu-title">轮播图管理</span>
        </div>
      </el-menu-item>

      <!-- 用户管理子菜单 -->
      <el-submenu index="user-management" class="submenu-modern">
        <template slot="title">
          <div class="menu-item-content">
            <i class="menu-icon el-icon-user-solid"></i>
            <span class="menu-title">用户管理</span>
          </div>
        </template>
        <el-menu-item index="/admin@管理员管理" class="submenu-item-modern">
          <div class="submenu-item-content">
            <i class="submenu-icon el-icon-s-custom"></i>
            <span class="submenu-title">管理员管理</span>
          </div>
        </el-menu-item>
        <el-menu-item index="/user@普通用户管理" class="submenu-item-modern">
          <div class="submenu-item-content">
            <i class="submenu-icon el-icon-user"></i>
            <span class="submenu-title">普通用户管理</span>
          </div>
        </el-menu-item>
      </el-submenu>

      <!-- 个人中心 -->
      <el-menu-item index="/center@个人中心" class="menu-item-modern">
        <div class="menu-item-content">
          <i class="menu-icon el-icon-setting"></i>
          <span class="menu-title">个人中心</span>
        </div>
      </el-menu-item>
    </el-menu>

    <!-- 折叠按钮 -->
    <div class="collapse-trigger" @click="toggleCollapse">
      <i :class="isCollapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
    </div>
  </el-aside>
</template>

<script>
  import { mapState } from 'vuex'

  export default {
    name: "ModernAside",
    data() {
      return {
        isCollapse: false
      }
    },
    computed: {
      ...mapState({
        activeMenuArrary: state => state.menu.activeMenuArrary,
        displayMenus: state => state.menu.displayMenus,
      })
    },
    methods: {
      // 处理菜单选择
      handleSelect(key) {
        const menu = key.split("@");
        const path = menu[0];
        const name = menu[1];

        // 添加页面切换动画
        this.animateMenuTransition(() => {
          this.$router.push({ path });
          this.$store.commit('menu/setActiveMenu', path);

          // 检查是否已存在该菜单项
          const exists = this.activeMenuArrary.some(item => item.url === path);
          if (!exists) {
            const param = { name, url: path };
            this.$store.commit('menu/addActiveMenu', param);
          }
        });
      },

      // 切换折叠状态
      toggleCollapse() {
        this.isCollapse = !this.isCollapse;
        this.$bus.$emit('collapse', this.isCollapse);
      },

      // 菜单切换动画
      animateMenuTransition(callback) {
        const menuItems = document.querySelectorAll('.menu-item-modern, .submenu-item-modern');
        menuItems.forEach((item, index) => {
          setTimeout(() => {
            item.style.transform = 'translateX(5px)';
            item.style.opacity = '0.8';
            setTimeout(() => {
              item.style.transform = 'translateX(0)';
              item.style.opacity = '1';
            }, 100);
          }, index * 20);
        });

        setTimeout(callback, 50);
      },

      // 初始化当前路由
      initCurrentRoute() {
        const currentPath = this.$route.path;
        const currentName = this.$route.name;

        this.$store.commit('menu/setActiveMenu', currentPath);

        // 检查是否已存在该菜单项
        const exists = this.activeMenuArrary.some(item => item.url === currentPath);
        if (!exists && currentName) {
          const param = { name: currentName, url: currentPath };
          this.$store.commit('menu/addActiveMenu', param);
        }
      }
    },

    mounted() {
      // 监听折叠状态变化
      this.$bus.$on('collapse', (collapsed) => {
        this.isCollapse = collapsed;
      });

      // 初始化当前路由
      this.initCurrentRoute();

      // 添加进入动画
      this.$nextTick(() => {
        const menuItems = document.querySelectorAll('.menu-item-modern, .submenu-modern');
        menuItems.forEach((item, index) => {
          item.style.opacity = '0';
          item.style.transform = 'translateX(-20px)';
          setTimeout(() => {
            item.style.transition = 'all 0.3s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateX(0)';
          }, index * 50 + 200);
        });
      });
    },

    beforeDestroy() {
      this.$bus.$off('collapse');
    }
  }
</script>
</script>

<style lang="scss" scoped>
  .modern-aside {
    background: var(--dark-gradient);
    backdrop-filter: blur(20px);
    border-right: 1px solid var(--glass-border);
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal) ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--primary-gradient);
      opacity: 0.1;
      z-index: 0;
    }
  }

  // Logo区域样式
  .logo-container {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid var(--glass-border);
    position: relative;
    z-index: 1;
    transition: all var(--transition-normal) ease;

    &.collapsed {
      padding: 16px 8px;

      .logo-wrapper {
        justify-content: center;
      }
    }
  }

  .logo-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all var(--transition-normal) ease;
  }

  .logo-image {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    object-fit: cover;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal) ease;

    &:hover {
      transform: scale(1.05) rotate(5deg);
      box-shadow: var(--shadow-lg);
    }
  }

  .logo-text {
    color: white;

    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 700;
      background: var(--soft-gradient);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      line-height: 1.2;
    }

    span {
      font-size: 11px;
      color: rgba(255, 255, 255, 0.7);
      font-weight: 400;
      letter-spacing: 0.5px;
    }
  }

  // 现代化菜单样式
  .modern-menu {
    height: calc(100vh - 160px);
    background: transparent;
    border: none;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 8px;
    position: relative;
    z-index: 1;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--primary-gradient);
      border-radius: 2px;
    }
  }

  // 菜单项样式
  .menu-item-modern {
    margin: 4px 0;
    border-radius: 12px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    transition: all var(--transition-normal) ease;
    overflow: hidden;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: var(--primary-gradient);
      transition: left var(--transition-normal) ease;
      z-index: -1;
    }

    &:hover {
      transform: translateX(8px);
      box-shadow: var(--shadow-md);
      border-color: var(--primary-color);

      &::before {
        left: 0;
      }

      .menu-item-content {
        color: white;

        .menu-icon {
          transform: scale(1.1);
          color: white;
        }
      }
    }

    &.is-active {
      background: var(--primary-gradient);
      border-color: var(--primary-color);
      box-shadow: var(--shadow-md);
      transform: translateX(4px);

      .menu-item-content {
        color: white;
        font-weight: 600;

        .menu-icon {
          color: white;
          transform: scale(1.05);
        }
      }
    }
  }

  .menu-item-content {
    display: flex;
    align-items: center;
    padding: 14px 16px;
    color: rgba(255, 255, 255, 0.9);
    transition: all var(--transition-fast) ease;

    .menu-icon {
      font-size: 18px;
      margin-right: 12px;
      color: rgba(255, 255, 255, 0.8);
      transition: all var(--transition-fast) ease;
      width: 20px;
      text-align: center;
    }

    .menu-title {
      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0.3px;
    }
  }

  // 子菜单样式
  .submenu-modern {
    margin: 4px 0;
    border-radius: 12px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    overflow: hidden;

    :deep(.el-submenu__title) {
      background: transparent !important;
      border-radius: 12px;
      transition: all var(--transition-normal) ease;

      &:hover {
        background: var(--primary-gradient) !important;
        transform: translateX(4px);

        .menu-item-content {
          color: white;

          .menu-icon {
            color: white;
            transform: scale(1.1);
          }
        }
      }
    }

    :deep(.el-menu) {
      background: rgba(0, 0, 0, 0.2) !important;
      backdrop-filter: blur(5px);
    }
  }

  .submenu-item-modern {
    background: rgba(0, 0, 0, 0.3) !important;
    margin: 2px 8px;
    border-radius: 8px;
    transition: all var(--transition-normal) ease;

    &:hover {
      background: var(--secondary-gradient) !important;
      transform: translateX(4px);

      .submenu-item-content {
        color: white;

        .submenu-icon {
          color: white;
          transform: scale(1.1);
        }
      }
    }

    &.is-active {
      background: var(--secondary-gradient) !important;

      .submenu-item-content {
        color: white;
        font-weight: 600;
      }
    }
  }

  .submenu-item-content {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    color: rgba(255, 255, 255, 0.8);
    transition: all var(--transition-fast) ease;

    .submenu-icon {
      font-size: 14px;
      margin-right: 10px;
      color: rgba(255, 255, 255, 0.7);
      transition: all var(--transition-fast) ease;
      width: 16px;
      text-align: center;
    }

    .submenu-title {
      font-size: 13px;
      font-weight: 400;
    }
  }

  // 折叠按钮
  .collapse-trigger {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: rgba(255, 255, 255, 0.8);
    transition: all var(--transition-normal) ease;
    z-index: 10;

    &:hover {
      background: var(--primary-gradient);
      color: white;
      transform: translateX(-50%) scale(1.1);
      box-shadow: var(--shadow-md);
    }

    i {
      font-size: 16px;
      transition: transform var(--transition-fast) ease;
    }
  }

  // 折叠状态样式调整
  :deep(.el-menu--collapse) {
    .menu-item-content,
    .submenu-item-content {
      justify-content: center;

      .menu-title,
      .submenu-title {
        display: none;
      }

      .menu-icon,
      .submenu-icon {
        margin-right: 0;
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .modern-aside {
      position: fixed;
      left: 0;
      top: 0;
      z-index: 1000;
      height: 100vh;
    }
  }
</style>