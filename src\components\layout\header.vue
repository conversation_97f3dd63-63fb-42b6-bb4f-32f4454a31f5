<template>
  <el-header class="modern-header">
    <!-- 左侧控制区域 -->
    <div class="header-left">
      <div class="collapse-btn" @click="changeCollapse">
        <i class="collapse-icon" :class="collapse ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
      </div>

      <!-- 实时时间显示 -->
      <div class="time-display">
        <div class="current-time">{{ currentTime }}</div>
        <div class="current-date">{{ currentDate }}</div>
      </div>
    </div>

    <!-- 中间搜索区域 -->
    <div class="header-center">
      <div class="search-container">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索功能模块..."
          prefix-icon="el-icon-search"
          class="search-input"
          @keyup.enter.native="handleSearch"
          clearable
        >
          <el-button slot="append" icon="el-icon-search" @click="handleSearch"></el-button>
        </el-input>
      </div>
    </div>

    <!-- 右侧功能区域 -->
    <div class="header-right">
      <!-- 通知中心 -->
      <div class="action-item notification" @click="showNotifications">
        <el-badge :value="notificationCount" :hidden="notificationCount === 0">
          <i class="el-icon-bell"></i>
        </el-badge>
      </div>

      <!-- 全屏切换 -->
      <div class="action-item fullscreen" @click="toggleFullscreen">
        <i :class="isFullscreen ? 'el-icon-copy-document' : 'el-icon-full-screen'"></i>
      </div>

      <!-- 主题切换 -->
      <div class="action-item theme-toggle" @click="toggleTheme">
        <i :class="isDarkTheme ? 'el-icon-sunny' : 'el-icon-moon'"></i>
      </div>

      <!-- 用户信息下拉菜单 -->
      <div class="user-profile">
        <el-dropdown trigger="click" @command="handleCommand" placement="bottom-end">
          <div class="user-info">
            <img class="user-avatar" :src="userAvatar" :alt="user.userName">
            <div class="user-details">
              <div class="user-name">{{ user.userName }}</div>
              <div class="user-role">管理员</div>
            </div>
            <i class="el-icon-arrow-down dropdown-arrow"></i>
          </div>

          <el-dropdown-menu slot="dropdown" class="modern-dropdown">
            <el-dropdown-item command="center" class="dropdown-item">
              <i class="el-icon-user"></i>
              <span>个人中心</span>
            </el-dropdown-item>
            <el-dropdown-item command="password" class="dropdown-item">
              <i class="el-icon-key"></i>
              <span>修改密码</span>
            </el-dropdown-item>
            <el-dropdown-item command="settings" class="dropdown-item">
              <i class="el-icon-setting"></i>
              <span>系统设置</span>
            </el-dropdown-item>
            <el-dropdown-item divided command="logout" class="dropdown-item logout">
              <i class="el-icon-switch-button"></i>
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
  </el-header>
</template>

<script>
  import { removeToken } from '@/utils/token'
  import { mapGetters } from 'vuex'
  import { logout } from '@/api/api'

  export default {
    name: "ModernHeader",
    data() {
      return {
        user: {
          avatar: "/img/avatar.jpg",
          userName: "管理员"
        },
        searchKeyword: "",
        collapse: false,
        currentTime: "",
        currentDate: "",
        notificationCount: 3,
        isFullscreen: false,
        isDarkTheme: false,
        timeInterval: null
      }
    },
    computed: {
      ...mapGetters('user', ['getUser']),

      userAvatar() {
        const host = this.$store.state.configure?.HOST || '';
        return host + this.user.avatar;
      }
    },
    methods: {
      // 切换侧边栏折叠状态
      changeCollapse() {
        this.collapse = !this.collapse;
        this.$bus.$emit('collapse', this.collapse);
      },

      // 更新时间显示
      updateTime() {
        const now = new Date();
        this.currentTime = now.toLocaleTimeString('zh-CN', {
          hour12: false,
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
        this.currentDate = now.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit'
        });
      },

      // 搜索功能
      handleSearch() {
        if (this.searchKeyword.trim()) {
          this.$message.info(`搜索功能: ${this.searchKeyword}`);
          // 这里可以实现实际的搜索逻辑
        }
      },

      // 显示通知
      showNotifications() {
        this.$message.info('通知中心功能开发中...');
      },

      // 切换全屏
      toggleFullscreen() {
        if (!this.isFullscreen) {
          const de = document.documentElement;
          if (de.requestFullscreen) {
            de.requestFullscreen();
          } else if (de.mozRequestFullScreen) {
            de.mozRequestFullScreen();
          } else if (de.webkitRequestFullScreen) {
            de.webkitRequestFullScreen();
          }
        } else {
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.mozCancelFullScreen) {
            document.mozCancelFullScreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          }
        }
      },

      // 切换主题
      toggleTheme() {
        this.isDarkTheme = !this.isDarkTheme;
        this.$message.info(`已切换到${this.isDarkTheme ? '深色' : '浅色'}主题`);
        // 这里可以实现主题切换逻辑
      },

      // 处理用户菜单命令
      handleCommand(command) {
        switch (command) {
          case "center":
            this.$router.push('/center');
            break;
          case "password":
            this.$bus.$emit('password', true);
            break;
          case "settings":
            this.$message.info('系统设置功能开发中...');
            break;
          case "logout":
            this.handleLogout();
            break;
        }
      },

      // 处理退出登录
      handleLogout() {
        this.$confirm('确认退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          logout().then(res => {
            if (res.code === 1000) {
              // 清除所有状态
              this.$store.commit('user/setToken', "");
              this.$store.commit('user/setUser', "");
              this.$store.commit('menu/setMenus', []);
              this.$store.commit('menu/setRoutes', []);
              this.$store.commit('menu/setDisplayMenus', []);
              this.$store.commit('menu/setBtnMenus', []);
              this.$store.commit("menu/setFlag", false);
              this.$store.commit('menu/setActiveMenuArrary', []);
              this.$store.commit('menu/setActiveMenu', "/index");

              removeToken();

              this.$message.success("退出登录成功");

              // 添加退出动画
              setTimeout(() => {
                this.$router.push("/login");
                setTimeout(() => {
                  window.location.reload();
                }, 500);
              }, 1000);
            } else {
              this.$message.warning(res.message);
            }
          }).catch(() => {
            this.$message.error('退出登录失败');
          });
        });
      },

      // 监听全屏状态变化
      handleFullscreenChange() {
        this.isFullscreen = !!(
          document.fullscreenElement ||
          document.mozFullScreenElement ||
          document.webkitFullscreenElement
        );
      }
    },

    created() {
      // 初始化时间
      this.updateTime();
    },

    mounted() {
      // 获取用户信息
      if (this.getUser) {
        this.user = JSON.parse(this.getUser);
      }

      // 启动时间更新定时器
      this.timeInterval = setInterval(this.updateTime, 1000);

      // 监听全屏状态变化
      document.addEventListener('fullscreenchange', this.handleFullscreenChange);
      document.addEventListener('mozfullscreenchange', this.handleFullscreenChange);
      document.addEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    },

    beforeDestroy() {
      // 清除定时器
      if (this.timeInterval) {
        clearInterval(this.timeInterval);
      }

      // 移除事件监听
      document.removeEventListener('fullscreenchange', this.handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', this.handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', this.handleFullscreenChange);
    }
  }
</script>
</script>

<style lang="scss" scoped>
  .modern-header {
    height: 70px !important;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--shadow-sm);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 24px;
    position: relative;
    z-index: 100;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--primary-gradient);
      opacity: 0.05;
      z-index: -1;
    }
  }

  // 左侧区域
  .header-left {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .collapse-btn {
    width: 44px;
    height: 44px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal) ease;

    &:hover {
      background: var(--primary-gradient);
      transform: scale(1.05);
      box-shadow: var(--shadow-md);

      .collapse-icon {
        color: white;
        transform: rotate(180deg);
      }
    }

    .collapse-icon {
      font-size: 18px;
      color: var(--text-primary);
      transition: all var(--transition-normal) ease;
    }
  }

  .time-display {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    .current-time {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-primary);
      font-family: 'Courier New', monospace;
    }

    .current-date {
      font-size: 12px;
      color: var(--text-secondary);
      margin-top: -2px;
    }
  }

  // 中间搜索区域
  .header-center {
    flex: 1;
    max-width: 500px;
    margin: 0 40px;
  }

  .search-container {
    position: relative;

    .search-input {
      :deep(.el-input__inner) {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        border: 1px solid var(--glass-border);
        border-radius: 25px;
        padding: 0 20px;
        height: 44px;
        font-size: 14px;
        color: var(--text-primary);
        transition: all var(--transition-normal) ease;

        &:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
          background: rgba(255, 255, 255, 0.9);
        }

        &::placeholder {
          color: var(--text-secondary);
        }
      }

      :deep(.el-input__prefix) {
        left: 15px;
        color: var(--text-secondary);
      }

      :deep(.el-input-group__append) {
        background: var(--primary-gradient);
        border: none;
        border-radius: 0 25px 25px 0;

        .el-button {
          background: transparent;
          border: none;
          color: white;

          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }
        }
      }
    }
  }

  // 右侧功能区域
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .action-item {
    width: 44px;
    height: 44px;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-normal) ease;
    position: relative;

    &:hover {
      background: var(--primary-gradient);
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);

      i {
        color: white;
        transform: scale(1.1);
      }
    }

    i {
      font-size: 18px;
      color: var(--text-primary);
      transition: all var(--transition-fast) ease;
    }

    &.notification {
      :deep(.el-badge__content) {
        background: var(--accent-color);
        border: 2px solid white;
        font-size: 10px;
        height: 18px;
        line-height: 14px;
        min-width: 18px;
        padding: 0;
        right: -5px;
        top: -5px;
      }
    }
  }

  // 用户信息区域
  .user-profile {
    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 8px 16px;
      background: var(--glass-bg);
      backdrop-filter: blur(10px);
      border: 1px solid var(--glass-border);
      border-radius: 25px;
      cursor: pointer;
      transition: all var(--transition-normal) ease;

      &:hover {
        background: var(--primary-gradient);
        transform: translateY(-2px);
        box-shadow: var(--shadow-md);

        .user-details {
          color: white;
        }

        .dropdown-arrow {
          color: white;
          transform: rotate(180deg);
        }
      }
    }

    .user-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid var(--glass-border);
      transition: all var(--transition-normal) ease;
    }

    .user-details {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      transition: color var(--transition-fast) ease;

      .user-name {
        font-size: 14px;
        font-weight: 600;
        color: var(--text-primary);
        line-height: 1.2;
      }

      .user-role {
        font-size: 12px;
        color: var(--text-secondary);
        line-height: 1.2;
      }
    }

    .dropdown-arrow {
      font-size: 12px;
      color: var(--text-secondary);
      transition: all var(--transition-normal) ease;
    }
  }

  // 下拉菜单样式
  :deep(.modern-dropdown) {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    box-shadow: var(--glass-shadow);
    padding: 8px 0;
    margin-top: 8px;

    .dropdown-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 20px;
      transition: all var(--transition-fast) ease;

      i {
        font-size: 16px;
        color: var(--text-secondary);
        width: 20px;
        text-align: center;
      }

      span {
        font-size: 14px;
        color: var(--text-primary);
        font-weight: 500;
      }

      &:hover {
        background: var(--primary-gradient);

        i, span {
          color: white;
        }
      }

      &.logout {
        border-top: 1px solid var(--glass-border);
        margin-top: 4px;

        i, span {
          color: #ff4757;
        }

        &:hover {
          background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);

          i, span {
            color: white;
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 1024px) {
    .header-center {
      max-width: 300px;
      margin: 0 20px;
    }

    .time-display {
      display: none;
    }
  }

  @media (max-width: 768px) {
    .modern-header {
      padding: 0 16px;
    }

    .header-center {
      display: none;
    }

    .user-details {
      display: none;
    }

    .action-item {
      width: 40px;
      height: 40px;
    }
  }
</style>