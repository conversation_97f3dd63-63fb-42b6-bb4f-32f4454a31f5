import Vue from 'vue'

// 创建一个全局事件总线
export const EventBus = new Vue()

// 导出常用的事件名称
export const EVENTS = {
  USER_INFO_UPDATED: 'userInfoUpdated',
  AVATAR_UPDATED: 'avatarUpdated',
  USER_LOGIN: 'userLogin',
  USER_LOGOUT: 'userLogout'
}

// 便捷方法
export const emitUserInfoUpdate = (userInfo) => {
  EventBus.$emit(EVENTS.USER_INFO_UPDATED, userInfo)
}

export const emitAvatarUpdate = (avatar) => {
  EventBus.$emit(EVENTS.AVATAR_UPDATED, avatar)
}

export default EventBus
