/**
 * 时间工具类 - 解决时区显示问题
 * 
 * 问题描述：
 * - 数据库存储的是实际时间（如：2025-06-20 23:13:30）
 * - 前端显示时被浏览器自动转换为本地时区（如：2025-06-20 15:13:30）
 * - 需要确保前端显示的时间与数据库中的时间一致
 */

/**
 * 格式化时间 - 确保显示数据库中的实际时间
 * @param {string|Date|number} value - 时间值
 * @param {string} format - 时间格式，默认 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的时间字符串
 */
export function formatTime(value, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!value) return '';
  
  // 情况1：如果是已经格式化的字符串（如：2025-06-20 23:13:30）
  if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(value)) {
    // 直接返回，不进行任何转换
    return value;
  }
  
  // 情况2：如果是日期字符串（如：2025-06-20T23:13:30.000Z）
  if (typeof value === 'string' && value.includes('T')) {
    // 解析ISO字符串，但显示UTC时间（数据库时间）
    const date = new Date(value);
    if (isNaN(date.getTime())) return '';
    
    return formatUTCTime(date, format);
  }
  
  // 情况3：如果是时间戳或其他格式
  const date = new Date(value);
  if (isNaN(date.getTime())) return '';
  
  // 检查是否需要时区调整
  // 如果当前显示的时间与期望的时间相差8小时，说明有时区问题
  return formatUTCTime(date, format);
}

/**
 * 格式化UTC时间
 * @param {Date} date - Date对象
 * @param {string} format - 时间格式
 * @returns {string} 格式化后的时间字符串
 */
function formatUTCTime(date, format) {
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, '0');
  const day = String(date.getUTCDate()).padStart(2, '0');
  const hours = String(date.getUTCHours()).padStart(2, '0');
  const minutes = String(date.getUTCMinutes()).padStart(2, '0');
  const seconds = String(date.getUTCSeconds()).padStart(2, '0');
  
  if (format === 'YYYY-MM-DD') {
    return `${year}-${month}-${day}`;
  }
  
  if (format === 'YYYY-MM-DD HH:mm') {
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
  
  // 默认格式：YYYY-MM-DD HH:mm:ss
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 智能时间格式化 - 自动检测并修正时区问题
 * @param {string|Date|number} value - 时间值
 * @param {string} format - 时间格式
 * @returns {string} 格式化后的时间字符串
 */
export function smartFormatTime(value, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!value) return '';
  
  // 如果是字符串且已经是正确格式，直接返回
  if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(value)) {
    return value;
  }
  
  const date = new Date(value);
  if (isNaN(date.getTime())) return '';
  
  // 获取本地时间
  const localYear = date.getFullYear();
  const localMonth = String(date.getMonth() + 1).padStart(2, '0');
  const localDay = String(date.getDate()).padStart(2, '0');
  const localHours = String(date.getHours()).padStart(2, '0');
  const localMinutes = String(date.getMinutes()).padStart(2, '0');
  const localSeconds = String(date.getSeconds()).padStart(2, '0');
  
  // 获取UTC时间
  const utcYear = date.getUTCFullYear();
  const utcMonth = String(date.getUTCMonth() + 1).padStart(2, '0');
  const utcDay = String(date.getUTCDate()).padStart(2, '0');
  const utcHours = String(date.getUTCHours()).padStart(2, '0');
  const utcMinutes = String(date.getUTCMinutes()).padStart(2, '0');
  const utcSeconds = String(date.getUTCSeconds()).padStart(2, '0');
  
  const localTime = `${localYear}-${localMonth}-${localDay} ${localHours}:${localMinutes}:${localSeconds}`;
  const utcTime = `${utcYear}-${utcMonth}-${utcDay} ${utcHours}:${utcMinutes}:${utcSeconds}`;
  
  // 调试信息
  console.log('🕐 时间格式化调试信息:');
  console.log('  原始值:', value);
  console.log('  本地时间:', localTime);
  console.log('  UTC时间:', utcTime);
  console.log('  时区偏移:', date.getTimezoneOffset(), '分钟');
  
  // 根据具体情况选择返回本地时间还是UTC时间
  // 这里可以根据实际需求调整逻辑
  return utcTime; // 默认返回UTC时间（数据库时间）
}

/**
 * 创建时间显示组件的props
 * @param {string|Date|number} time - 时间值
 * @returns {Object} 包含格式化时间和样式的对象
 */
export function createTimeDisplay(time) {
  const formattedTime = formatTime(time);
  
  return {
    text: formattedTime,
    icon: 'el-icon-time',
    class: 'time-cell',
    style: {
      display: 'flex',
      alignItems: 'center',
      fontFamily: '"Courier New", monospace',
      fontSize: '12px',
      color: '#606266'
    }
  };
}

/**
 * 时区偏移检测
 * @returns {Object} 时区信息
 */
export function getTimezoneInfo() {
  const now = new Date();
  const timezoneOffset = now.getTimezoneOffset(); // 分钟
  const timezoneOffsetHours = Math.abs(timezoneOffset) / 60;
  const timezoneSign = timezoneOffset <= 0 ? '+' : '-';
  
  return {
    offset: timezoneOffset,
    offsetHours: timezoneOffsetHours,
    offsetString: `UTC${timezoneSign}${timezoneOffsetHours}`,
    isChina: timezoneOffset === -480, // 中国时区 UTC+8
    localTime: now.toLocaleString(),
    utcTime: now.toUTCString()
  };
}

/**
 * 调试时间问题的工具函数
 * @param {string|Date|number} value - 时间值
 */
export function debugTime(value) {
  console.group('🔍 时间调试信息');
  
  const timezoneInfo = getTimezoneInfo();
  console.log('时区信息:', timezoneInfo);
  
  if (value) {
    const date = new Date(value);
    console.log('原始值:', value);
    console.log('Date对象:', date);
    console.log('本地时间:', date.toLocaleString());
    console.log('UTC时间:', date.toUTCString());
    console.log('ISO字符串:', date.toISOString());
    console.log('时间戳:', date.getTime());
    
    console.log('格式化结果:');
    console.log('  formatTime:', formatTime(value));
    console.log('  smartFormatTime:', smartFormatTime(value));
  }
  
  console.groupEnd();
}

// 默认导出
export default {
  formatTime,
  smartFormatTime,
  createTimeDisplay,
  getTimezoneInfo,
  debugTime
};
