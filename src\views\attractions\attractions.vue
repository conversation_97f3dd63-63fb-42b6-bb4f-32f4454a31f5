<template>
  <div class="attractions-page">
    <headers />
    <div class="attractions-main-content">
      <el-card class="search-panel" shadow="never">
        <el-input
          size="small"
          class="search-input"
          v-model="searchParams.name"
          placeholder="请输入景点名称"
          clearable
          @keyup.enter.native="handleSearch"
          prefix-icon="el-icon-search"
        />
        <el-button
          size="small"
          class="search-button"
          type="primary"
          @click="handleSearch"
        >
          搜索
        </el-button>
      </el-card>

      <div class="list-container">
        <!-- 加载状态 -->
        <LoadingSpinner
          v-if="isLoading"
          text="正在加载景点信息..."
          spinner-type="pulse"
          size="large"
        />

        <!-- 景点列表 -->
        <el-row :gutter="24" class="attractions-list-row" v-else-if="attractionsList.length > 0">
          <el-col
            :xs="24" :sm="12" :md="8" :lg="6"
            v-for="item in attractionsList"
            :key="item.id"
            class="attraction-col"
          >
            <el-card shadow="hover" class="attraction-card" :body-style="{ padding: '0px' }">
              <el-image
                class="attraction-image"
                :src="item.images && item.images.split(',')[0]"
                fit="cover"
                lazy
              >
                <div slot="placeholder" class="image-slot loading">
                  <LoadingSpinner spinner-type="dots" size="small" text="" />
                </div>
                <div slot="error" class="image-slot error">
                  <i class="el-icon-picture-outline"></i>
                  <span>图片加载失败</span>
                </div>
              </el-image>
              <div class="attraction-info">
                <h3 class="attraction-name" :title="item.name">{{ item.name | truncate(20) }}</h3>
                <p class="attraction-introduce" :title="item.introduce">
                  {{ item.introduce | truncate(45) }}
                </p>
                <el-button
                  type="primary"
                  plain
                  size="small"
                  class="details-button"
                  @click="navigateToDetail(item.id)"
                >
                  <i class="el-icon-view"></i>
                  查看详情
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 空状态 -->
        <EmptyState
          v-else
          title="暂无相关景点"
          description="当前没有找到符合条件的景点信息，请尝试调整搜索条件或稍后再试"
          icon-class="el-icon-location-outline"
          action-text="重新搜索"
          action-icon="el-icon-refresh"
          size="large"
          @action="handleRetry"
        />
      </div>

      <el-pagination
        v-if="totalItems > 0 && attractionsList.length > 0"
        class="attractions-pagination"
        background
        :current-page.sync="searchParams.pageNumber"
        :page-size="searchParams.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="totalItems"
        @current-change="handlePageChange"
      />
    </div>
    <bottoms />
  </div>
</template>

<script>
import { getSysAttractionsPage } from '../../api/api'; // 假设api路径正确
import headers from '@/components/header';
import bottoms from '@/components/bottom';
import LoadingSpinner from '@/components/LoadingSpinner';
import EmptyState from '@/components/EmptyState';

export default {
  name: 'AttractionsPage',
  components: {
    headers,
    bottoms,
    LoadingSpinner,
    EmptyState,
  },
  data() {
    return {
      searchParams: {
        name: '',
        state: 1, // 假设1为有效状态
        pageSize: 12,
        pageNumber: 1,
      },
      totalItems: 0,
      attractionsList: [],
      isLoading: false,
    };
  },
  filters: {
    truncate(text, length, suffix = '...') {
      if (typeof text !== 'string') return '';
      if (text.length > length) {
        return text.substring(0, length) + suffix;
      }
      return text;
    },
  },
  methods: {
    async fetchAttractionsData() {
      this.isLoading = true;
      try {
        // 实际项目中，分页参数可能需要后端调整，确保后端能正确接收 pageNumber 和 pageSize
        const params = {
          ...this.searchParams,
          // pageNum: this.searchParams.pageNumber, // 如果后端参数名不同
        };
        const res = await getSysAttractionsPage(params);
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.attractionsList = res.data.records || [];
          this.totalItems = res.data.total || 0;
        } else {
          this.$message.error(res.message || '获取景点数据失败');
          this.attractionsList = [];
          this.totalItems = 0;
        }
      } catch (error) {
        console.error('获取景点列表失败:', error);
        this.$message.error('网络繁忙，请稍后重试');
        this.attractionsList = [];
        this.totalItems = 0;
      } finally {
        this.isLoading = false;
      }
    },
    handleSearch() {
      this.searchParams.pageNumber = 1;
      this.fetchAttractionsData();
    },
    navigateToDetail(id) {
      this.$router.push({ path: '/attractionsInfo', query: { id } });
    },
    handlePageChange(page) {
      this.searchParams.pageNumber = page;
      this.fetchAttractionsData();
    },
    handleRetry() {
      this.searchParams.name = '';
      this.searchParams.pageNumber = 1;
      this.fetchAttractionsData();
    },
  },
  created() {
    this.fetchAttractionsData();
  },
};
</script>
<style scoped lang="scss">
// === 现代化设计变量 ===
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$text-color-primary: #1f2937;
$text-color-regular: #4b5563;
$text-color-secondary: #6b7280;
$border-color-light: #e5e7eb;
$bg-color-page: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
$card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$card-hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$card-border-radius: 16px;

// === 页面容器现代化设计 ===
.attractions-page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  background: $bg-color-page;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;

  // 添加装饰性背景
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 400px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 100%);
    z-index: -1;
  }
}

.attractions-main-content {
  width: 90%;
  max-width: 1400px;
  margin: 30px auto;
  flex-grow: 1;
  position: relative;
  z-index: 1;
}

// === 搜索面板现代化设计 ===
.search-panel {
  margin-bottom: 32px;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);

  .el-card__body {
    display: flex;
    align-items: center;
    padding: 24px;
  }

  .search-input {
    width: 400px;
    margin-right: 20px;

    ::v-deep .el-input__inner {
      border-radius: 12px;
      border: 2px solid #e5e7eb;
      padding: 12px 16px;
      font-size: 16px;
      transition: all 0.3s ease;

      &:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }
    }

    ::v-deep .el-input__prefix {
      left: 16px;
      color: #6b7280;
    }
  }

  .search-button {
    padding: 12px 32px;
    border-radius: 12px;
    background: $primary-gradient;
    border: none;
    font-weight: 500;
    font-size: 16px;
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    }
  }
}

// --- List Container ---
.list-container {
  min-height: 300px; // 给加载状态一个最小高度
}

// --- Attraction Card ---
.attraction-col {
  margin-bottom: 20px; // 卡片列的下边距（响应式换行时生效）
}

.attraction-card {
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  transition: transform 0.25s ease-in-out, box-shadow 0.25s ease-in-out;
  height: 100%; // 使同一行的卡片等高
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-4px);
    box-shadow: $card-hover-shadow;
  }

  .attraction-image {
    width: 100%;
    height: 180px; // 统一图片高度
    display: block; // 消除图片下方间隙
    background-color: #eee; // 图片未加载时的背景色
  }

  .image-slot {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: $text-color-secondary;
    font-size: 13px;
    text-align: center;

    &.loading i {
      font-size: 24px;
      color: #667eea;
      animation: rotating 2s linear infinite;
    }
    &.error i {
      font-size: 28px;
      margin-bottom: 8px;
    }
  }

  .attraction-info {
    padding: 12px 15px; // 内容区域内边距
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    text-align: left; // 文字左对齐
  }

  .attraction-name {
    font-size: 16px;
    color: $text-color-primary;
    font-weight: 500; // 调整字重
    margin: 0 0 6px 0;
    line-height: 1.4;
    // CSS 实现最多两行文字截断
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    min-height: calc(1.4em * 2); // 预留两行的高度
  }

  .attraction-introduce {
    font-size: 13px;
    color: $text-color-regular;
    line-height: 1.5;
    margin-bottom: 12px;
    flex-grow: 1;
    // CSS 实现最多三行文字截断
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3; 
    -webkit-box-orient: vertical;
    min-height: calc(1.5em * 3); // 预留三行的高度
  }

  .details-button {
    width: 100%;
    margin-top: auto; // 将按钮推到卡片底部
    background-color: transparent; // Element UI plain 按钮的背景通常是透明的
    // border-color: lighten($primary-color, 10%);
    // color: $primary-color;
    // &:hover, &:focus {
    //   background-color: lighten($primary-color, 40%);
    //   border-color: lighten($primary-color, 10%);
    //   color: $primary-color;
    // }
  }
}

// --- Empty State ---
.attractions-empty {
  width: 100%;
  padding: 30px 0; // 调整上下内边距
  min-height: 300px; // 确保空状态也有一定高度
  display: flex;
  justify-content: center;
  align-items: center;
}

// --- Pagination ---
.attractions-pagination {
  margin-top: 30px; // 与列表或空状态的间距
  padding-bottom: 20px; // 底部留白
  text-align: center;
}

// 可以在此覆盖 Element UI 分页的具体样式
// .attractions-pagination ::v-deep {
//   .el-pager li:not(.disabled).active {
//     background-color: $primary-color;
//     color: #fff;
//   }
//   .el-pagination__jump .el-input__inner {
//     height: 28px;
//   }
// }

// Element UI loading 动画 (如果使用 v-loading)
@keyframes rotating {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

</style>