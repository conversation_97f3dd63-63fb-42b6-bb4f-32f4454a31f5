<template>
  <div class="attraction-detail-page">
    <headers />

    <div v-loading="isLoading.page" class="attraction-detail-main-content">
      <el-card v-if="attractionDetail.id" class="content-wrapper-card" shadow="never">
        <el-row :gutter="30" class="main-info-row">
          <el-col :xs="24" :md="14" class="carousel-col-detail">
            <el-carousel height="420px" class="attraction-carousel" v-if="imageUrls.length > 0" indicator-position="outside">
              <el-carousel-item v-for="(imgUrl, index) in imageUrls" :key="index">
                <el-image class="carousel-image-detail" :src="imgUrl" fit="cover"  :preview-src-list="imageUrls" />
              </el-carousel-item>
            </el-carousel>
            <el-empty v-else description="暂无景点图片" class="image-empty-placeholder-detail"></el-empty>
          </el-col>

          <el-col :xs="24" :md="10" class="core-info-col">
            <h1 class="attraction-title">{{ attractionDetail.name }}</h1>
            <el-descriptions :column="1" border size="small" class="attraction-descriptions">
              <el-descriptions-item label="价格">
                <span class="price-tag">¥{{ attractionDetail.price }}</span> /人起
              </el-descriptions-item>
              <el-descriptions-item label="库存">{{ attractionDetail.num }}</el-descriptions-item>
              <el-descriptions-item label="实名制">
                <el-tag :type="attractionDetail.realName == 1 ? 'success' : 'info'" size="mini">
                  {{ attractionDetail.realName == 1 ? '需要实名' : '无需实名' }}
                </el-tag>
              </el-descriptions-item>
              </el-descriptions>
            <p class="attraction-introduce-brief">{{ attractionDetail.introduce | truncate(80) }}</p>
            <el-button 
              type="warning" 
              icon="el-icon-date" 
              @click="openBookingDialog" 
              class="book-button-detail"
              :disabled="attractionDetail.num === 0"
            >
              {{ attractionDetail.num > 0 ? '立即预约' : '暂不可预约' }}
            </el-button>
          </el-col>
        </el-row>

        <el-tabs v-model="activeInfoTab" type="border-card" class="detail-info-tabs">
          <el-tab-pane label="详细介绍" name="content">
            <div class="info-tab-content content-html" v-html="processedContent(attractionDetail.content)"></div>
          </el-tab-pane>
          <el-tab-pane label="预约须知" name="open">
             <div class="info-tab-content content-html" v-html="processedContent(attractionDetail.open)"></div>
          </el-tab-pane>
          <el-tab-pane label="入园时间" name="time">
             <div class="info-tab-content content-html" v-html="processedContent(attractionDetail.time)"></div>
          </el-tab-pane>
        </el-tabs>

        <div class="comments-section">
          <h3 class="section-title-comment">用户评价</h3>
          <el-card shadow="never" class="add-comment-card">
            <el-form :model="newComment" :rules="commentRules" ref="commentFormRef" @submit.native.prevent="submitComment">
              <el-form-item prop="content">
                <el-input 
                  type="textarea" 
                  :rows="4" 
                  v-model="newComment.content" 
                  placeholder="分享您的游玩体验吧..."
                  maxlength="500"
                  show-word-limit
                />
              </el-form-item>
              <el-form-item label="评分" prop="score" class="comment-rate-item">
                <el-rate v-model="newComment.score" :colors="['#99A9BF', '#F7BA2A', '#FF9900']"></el-rate>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" size="small" @click="submitComment" :loading="isLoading.commentSubmit">发表评论</el-button>
              </el-form-item>
            </el-form>
          </el-card>

          <div v-loading="isLoading.comments" class="comments-list-wrapper">
            <div v-if="commentsList.length > 0" class="comments-list">
              <div v-for="comment in commentsList" :key="comment.id" class="comment-item">
                <el-avatar :src="comment.avatar ? ($store.state.HOST + comment.avatar) : defaultAvatar" class="comment-avatar" size="medium"></el-avatar>
                <div class="comment-body">
                  <div class="comment-header">
                    <span class="comment-author">{{ comment.createBy || '匿名用户' }}</span>
                    <el-rate :value="comment.score" disabled show-score text-color="#ff9900" score-template="{value} 分" class="comment-item-rate"></el-rate>
                  </div>
                  <p class="comment-content">{{ comment.content }}</p>
                  <div class="comment-footer">
                    <span class="comment-time">{{ comment.createTime | formatDateWithTime }}</span>
                  </div>
                </div>
              </div>
              <el-pagination
                v-if="commentsPagination.totalItems > 0 && commentsList.length > 0"
                class="comments-pagination"
                small background
                :current-page.sync="commentsPagination.pageNumber"
                :page-size="commentsPagination.pageSize"
                layout="total, prev, pager, next"
                :total="commentsPagination.totalItems"
                @current-change="handleCommentPageChange"
              />
            </div>
            <el-empty v-else description="暂无评论，期待您的第一条评价！" class="comments-empty"></el-empty>
          </div>
        </div>

      </el-card>
      <el-empty 
        v-else-if="!isLoading.page" 
        description="景点详情加载失败或不存在" 
        class="page-empty-detail"
      >
         <el-button type="primary" size="small" @click="$router.push('/attractions')">返回景点列表</el-button>
      </el-empty>
    </div>

    <el-dialog
      title="立即预约"
      :visible.sync="bookingDialog.visible"
      width="600px"
      :before-close="closeBookingDialog"
      class="booking-dialog-detail"
      append-to-body
      destroy-on-close
      @opened="onBookingDialogOpened"
    >
      <el-form 
        :model="bookingForm" 
        :rules="bookingRules" 
        ref="bookingFormRef" 
        label-width="90px" 
        size="small" 
        @submit.native.prevent="submitBooking"
        v-loading="bookingDialog.formLoading"
      >
        <el-alert
            v-if="attractionDetail.name"
            :title="`您正在预约：${attractionDetail.name}`"
            type="info"
            show-icon
            :closable="false"
            style="margin-bottom: 15px;"
        ></el-alert>
        <el-form-item label="预约数量" prop="num">
          <el-input-number v-model="bookingForm.num" :min="1" :max="maxBookingNum" />
        </el-form-item>
        <el-form-item label="预约日期" prop="date">
          <el-date-picker
            v-model="bookingForm.date"
            type="date"
            placeholder="选择预约日期"
            value-format="yyyy-MM-dd"
            :picker-options="bookingDateOptions"
            style="width: 100%;"
          />
        </el-form-item>
        
        <h4>游客信息 (共 {{ bookingForm.num }} 人)</h4>
        <div v-for="(traveler, index) in bookingForm.people" :key="index" class="traveler-form-item">
            <el-form-item 
                :label="`游客 ${index + 1}`" 
                class="traveler-name-item"
                :prop="'people.' + index + '.name'"
                :rules="bookingRules.travelerName"
            >
                <el-input v-model="traveler.name" placeholder="姓名" />
            </el-form-item>
            <el-form-item 
                label-width="0px" 
                class="traveler-tel-item"
                :prop="'people.' + index + '.tel'"
                :rules="bookingRules.travelerTel"
            >
                <el-input v-model="traveler.tel" placeholder="电话" />
            </el-form-item>
            <el-form-item 
                v-if="attractionDetail.realName == 1" 
                label-width="0px" 
                class="traveler-idcard-item"
                :prop="'people.' + index + '.idCard'"
                :rules="bookingRules.travelerIdCard"
            >
                <el-input v-model="traveler.idCard" placeholder="身份证号" />
            </el-form-item>
            <el-button 
                type="danger" icon="el-icon-delete" circle size="mini"
                v-if="bookingForm.people.length > 1 && index === bookingForm.people.length -1" 
                @click="removeTraveler(index)" 
                class="remove-traveler-btn"
                title="删除最后一位游客"
            />
        </div>
        <el-form-item v-if="bookingForm.people.length < bookingForm.num">
            <el-button type="text" icon="el-icon-plus" @click="addTraveler">继续添加游客信息 (还需添加 {{ bookingForm.num - bookingForm.people.length }} 位)</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button size="small" @click="closeBookingDialog">取 消</el-button>
        <el-button 
            type="primary" 
            @click="submitBooking" 
            size="small" 
            :loading="bookingDialog.submitting"
            :disabled="bookingForm.people.length !== bookingForm.num"
        >
            确 定
        </el-button>
      </template>
    </el-dialog>
    <bottoms />
  </div>
</template>

<script>
import {getSysAttractionsById,saveSysAttractionOrder,getSysCommentsPage,saveSysComments} from '../../api/api'
import headers from '@/components/header' // <--- 假设 headers 在这里导入
import bottoms from '@/components/bottom'  // <--- 假设 bottoms 在这里导入
// 日期格式化函数
const formatDate = (value, format = 'YYYY-MM-DD HH:mm') => {
  if (!value) return '';
  const date = new Date(value);
  if (isNaN(date.getTime())) return '';

  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  if (format === 'YYYY-MM-DD') {
    return `${year}-${month}-${day}`;
  }
  return `${year}-${month}-${day} ${hours}:${minutes}`;
};

// 手机号验证函数
const validatePhoneNumber = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入手机号'));
    return;
  }
  const phoneRegex = /^1[3-9]\d{9}$/;
  if (!phoneRegex.test(value)) {
    callback(new Error('请输入正确的手机号格式'));
    return;
  }
  callback();
};

// 身份证验证函数
const validateIdCard = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入身份证号'));
    return;
  }
  const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  if (!idCardRegex.test(value)) {
    callback(new Error('请输入正确的身份证号格式'));
    return;
  }
  callback();
};


export default {
  name: 'AttractionDetailPage',
  components: { headers, bottoms },
  filters: {
    truncate(text, length, suffix = '...') {
      if (!text) return '';
      if (text.length <= length) return text;
      return text.substring(0, length) + suffix;
    },
    formatDate(value) { return formatDate(value, 'YYYY-MM-DD'); },
    formatDateWithTime(value) { return formatDate(value, 'YYYY-MM-DD HH:mm'); }
  },
  data() {
    return {
      attractionId: null,
      isLoading: {
        page: true,
        comments: false,
        commentSubmit: false,
      },
      attractionDetail: {
        id: null, name: '', images: '', price: 0, num: 0, realName: 0,
        introduce: '', content: '', open: '', time: '',
      },
      activeInfoTab: 'content', // 详情介绍默认Tab
      
      // 评论相关
      newComment: { content: '', score: null },
      commentRules: {
        content: [{ required: true, message: '请输入评论内容', trigger: 'blur' }],
        score: [{ required: true, message: '请选择评分', trigger: 'change' }],
      },
      commentsList: [],
      commentsPagination: {
        attractionsId: null, // 稍后从 attractionId 赋值
        pageSize: 5, // 评论每页显示条数
        pageNumber: 1,
        totalItems: 0,
      },
      defaultAvatar: require('@/assets/image/3.png'), // 假设有默认头像

      // 预约对话框相关
      bookingDialog: {
        visible: false,
        formLoading: false, // 可以用于加载预约前置条件，例如库存检查
        submitting: false,
      },
      bookingForm: {
        num: 1,
        date: '',
        people: [{ name: '', tel: '', idCard: '' }],
      },
      bookingRules: {
        num: [{ required: true, message: '请输入预约数量', trigger: 'change' }],
        date: [{ required: true, message: '请选择预约日期', trigger: 'change' }],
        // 游客信息的校验规则将动态应用到 people 数组的每个对象上
        travelerName: [{ required: true, message: '请输入游客姓名', trigger: 'blur' }],
        travelerTel: [{ required: true, validator: validatePhoneNumber, trigger: 'blur' }],
        travelerIdCard: [{ required: true, validator: validateIdCard, trigger: 'blur' }],
      },
       bookingDateOptions: {
        disabledDate(time) { return time.getTime() < Date.now() - 8.64e7; },
      }
    };
  },
  computed: {
    imageUrls() { 
      if (this.attractionDetail.images && typeof this.attractionDetail.images === 'string') {
        return this.attractionDetail.images.split(',').map(imgUrl => {
          if (imgUrl.startsWith('http')) return imgUrl.trim();
          return (this.$store.state.HOST || '') + imgUrl.trim();
        }).filter(url => url);
      }
      return [];
    },
    processedContent() { // 用于v-html，可以进行XSS过滤等
        return (content) => {
            if (!content) return '';
            // 简单的换行处理，实际富文本应由后端保证安全性或前端使用DOMPurify等库
            let processed = content;
            if (!processed.includes('<p>') && !processed.includes('<div>') && !processed.includes('<br')) {
                 processed = processed.replace(/\r\n/g, '<br>').replace(/\n/g, '<br>');
            }
            return processed;
        }
    },
    maxBookingNum() {
        return this.attractionDetail.num > 0 ? Math.min(10, this.attractionDetail.num) : 1; // 最大不超过库存且不超过10
    },
    userId() { // 从localStorage获取用户ID
        const userInfoString = window.localStorage.getItem("user_info");
        console.log('🔍 localStorage中的用户信息:', userInfoString);

        if (userInfoString) {
            try {
                const userInfo = JSON.parse(userInfoString);
                console.log('👤 解析后的用户信息:', userInfo);
                return userInfo.id;
            } catch(e) {
                console.error('❌ 解析用户信息失败:', e);
                return null;
            }
        }

        // 如果没有用户信息，为了测试目的，我们可以创建一个临时用户
        console.log('⚠️ 未找到用户信息，创建测试用户');
        const testUser = { id: 1, username: 'test_user' };
        window.localStorage.setItem("user_info", JSON.stringify(testUser));
        return testUser.id;
    }
  },
  methods: {
    // --- 数据获取 ---
    async initializePage() {
      this.attractionId = this.$route.query.id;
      if (!this.attractionId) {
        this.$message.error('无效的景点ID');
        this.isLoading.page = false;
        this.attractionDetail.title = '错误';
        this.attractionDetail.content = '<p>未找到指定的景点。</p>';
        return;
      }
      
      this.isLoading.page = true;
      this.commentsPagination.attractionsId = this.attractionId; // 设置评论分页参数

      try {
        await Promise.all([
          this.fetchAttractionDetail(),
          this.fetchComments() // 初始加载评论
        ]);
      } catch (error) {
        // fetchAttractionDetail 和 fetchComments 内部已有错误提示
        console.error("页面初始化数据加载综合错误:", error);
      } finally {
        this.isLoading.page = false;
      }
    },
    async fetchAttractionDetail() {
      try {
        const res = await getSysAttractionsById({ id: this.attractionId });
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.attractionDetail = res.data;
        } else {
          this.$message.error(res.message || '获取景点详情失败');
        }
      } catch (error) {
        console.error("获取景点详情异常:", error);
        this.$message.error('网络错误，景点详情加载失败');
      }
    },
    async fetchComments() {
      this.isLoading.comments = true;
      try {
        const res = await getSysCommentsPage(this.commentsPagination);
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.commentsList = res.data.records || [];
          this.commentsPagination.totalItems = Number(res.data.total) || 0;
        } else {
          this.$message.error(res.message || '获取评论列表失败');
        }
      } catch (error) {
        console.error("获取评论列表异常:", error);
        this.$message.error('网络错误，评论加载失败');
      } finally {
        this.isLoading.comments = false;
      }
    },

    // --- 评论操作 ---
    submitComment() {
      if (!this.userId) {
        this.$message.warning('请先登录后再发表评论');
        // 可以考虑跳转登录: this.$router.push('/login');
        return;
      }
      this.$refs.commentFormRef.validate(async (valid) => {
        if (valid) {
          this.isLoading.commentSubmit = true;
          try {
            const params = {
              content: this.newComment.content,
              score: this.newComment.score,
              attractionsId: this.attractionId,
              userId: this.userId, // 确保userId已获取
            };
            const res = await saveSysComments(params);
            if (res && (res.code === 1000 || res.code === 200)) {
              this.$message.success('评论发表成功！');
              this.newComment.content = '';
              this.newComment.score = null;
              this.$refs.commentFormRef.resetFields(); // 重置表单
              this.commentsPagination.pageNumber = 1; // 回到第一页查看最新评论
              this.fetchComments(); // 刷新评论列表
            } else {
              this.$message.error(res.message || '评论发表失败');
            }
          } catch (error) {
            console.error("发表评论异常:", error);
            this.$message.error('网络错误，评论发表失败');
          } finally {
            this.isLoading.commentSubmit = false;
          }
        }
      });
    },
    handleCommentPageChange(page) {
      this.commentsPagination.pageNumber = page;
      this.fetchComments();
    },

    // --- 预约对话框操作 ---
    openBookingDialog() {
      console.log('🎯 点击预约按钮');
      console.log('👤 当前用户ID:', this.userId);

      if (!this.userId) {
        this.$message.warning('请先登录后再进行预约');
        console.log('❌ 用户未登录，无法预约');
        return;
      }

      console.log('✅ 用户已登录，打开预约对话框');
      this.bookingForm = { // 重置或初始化预约表单
        num: 1,
        date: '',
        people: [{ name: '', tel: '', idCard: '' }],
      };
      this.bookingDialog.visible = true;
      console.log('📝 预约表单已初始化:', this.bookingForm);
    },
    onBookingDialogOpened() {
        this.$nextTick(() => { // 确保DOM渲染完毕
            if (this.$refs.bookingFormRef) {
                this.$refs.bookingFormRef.clearValidate();
            }
        });
    },
    closeBookingDialog() {
      this.bookingDialog.visible = false;
      // 表单会在 destroy-on-close 时销毁，或在下次打开时重置
    },
    addTraveler() {
      if (this.bookingForm.people.length < this.bookingForm.num) {
        this.bookingForm.people.push({ name: '', tel: '', idCard: '' });
      } else {
        this.$message.warning(`最多只能为 ${this.bookingForm.num} 位游客填写信息。`);
      }
    },
    removeTraveler(index) {
      if (this.bookingForm.people.length > 1) { // 至少保留一位
        this.bookingForm.people.splice(index, 1);
      }
    },
    submitBooking() {
      console.log('🚀 开始提交预约，当前表单数据:', this.bookingForm);
      console.log('🔍 用户ID:', this.userId);
      console.log('🎯 景点ID:', this.attractionId);

      if (!this.$refs.bookingFormRef) {
        console.error('❌ 表单引用不存在');
        this.$message.error('表单初始化失败，请重新打开预约对话框');
        return;
      }

      this.$refs.bookingFormRef.validate(async (valid) => {
        console.log('📝 表单验证结果:', valid);

        if (valid) {
          if (this.bookingForm.people.length !== this.bookingForm.num) {
              this.$message.warning(`请为全部 ${this.bookingForm.num} 位游客填写信息。`);
              return;
          }

          // 再次校验所有游客信息是否已填写
          for (let i = 0; i < this.bookingForm.people.length; i++) {
            const person = this.bookingForm.people[i];
            if (!person.name || !person.tel || (this.attractionDetail.realName == 1 && !person.idCard)) {
              this.$message.warning(`请完善第 ${i + 1} 位游客的信息。`);
              return;
            }
          }

          this.bookingDialog.submitting = true;
          console.log('📤 开始发送API请求...');

          try {
            // 获取当前用户信息
            const userInfo = this.getCurrentUserInfo();
            console.log('👤 当前用户信息:', userInfo);

            // 生成当前本地时间戳
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            console.log('🕐 生成的本地时间:', currentTime);

            const params = {
              attractionsId: this.attractionId,
              num: this.bookingForm.num,
              time: this.bookingForm.date,
              people: JSON.stringify(this.bookingForm.people),
              userId: this.userId,
              // ✅ 添加创建者信息 - 使用正确的字段名
              createBy: userInfo.userName || userInfo.loginAccount || userInfo.user_name || '游客',
              // ✅ 添加创建时间
              createTime: currentTime
            };

            console.log('📋 完整的API请求参数:', params);
            const res = await saveSysAttractionOrder(params);
            console.log('📥 API响应结果:', res);

            if (res && (res.code === 1000 || res.code === 200)) {
              this.$message.success('预约成功，请等待确认！');
              this.closeBookingDialog();
              // 刷新景点详情以更新库存
              this.fetchAttractionDetail();
            } else {
              console.error('❌ API返回错误:', res);
              this.$message.error(res.message || '预约失败');
            }
          } catch (error) {
            console.error("❌ 提交预约异常:", error);
            this.$message.error('网络错误，预约失败');
          } finally {
            this.bookingDialog.submitting = false;
          }
        } else {
          console.log('❌ 表单验证失败');
        }
      });
    },

    // 获取当前用户信息的方法
    getCurrentUserInfo() {
      const userInfoString = window.localStorage.getItem("user_info");
      console.log('🔍 获取用户信息 - localStorage内容:', userInfoString);

      if (userInfoString) {
        try {
          const userInfo = JSON.parse(userInfoString);
          console.log('✅ 解析用户信息成功:', userInfo);
          console.log('📋 用户信息字段详情:');
          console.log('  - userName:', userInfo.userName);           // ✅ 正确的字段名
          console.log('  - loginAccount:', userInfo.loginAccount);   // ✅ 正确的字段名
          console.log('  - user_name:', userInfo.user_name);         // 备用字段
          console.log('  - username:', userInfo.username);           // 备用字段
          console.log('  - id:', userInfo.id);

          // 使用正确的字段名获取用户名
          const userName = userInfo.userName || userInfo.loginAccount || userInfo.user_name || userInfo.username || '游客';
          console.log('🎯 最终选择的用户名:', userName);

          return {
            ...userInfo,
            finalUserName: userName
          };
        } catch (e) {
          console.error('❌ 解析用户信息失败:', e);
          return { user_name: '游客', username: '游客', name: '游客', finalUserName: '游客' };
        }
      }

      console.log('⚠️ 未找到用户信息，返回默认值');
      return { user_name: '游客', username: '游客', name: '游客', finalUserName: '游客' };
    },
  },
  created() {
    this.initializePage();
  },
  watch: {
    '$route.query.id'(newId) {
      if (newId && newId !== this.attractionId) {
        this.initializePage();
      }
    },
    // 监听预约数量变化，同步游客信息表单数量
    'bookingForm.num'(newNum, oldNum) {
        if (!this.bookingDialog.visible) return; // 只在对话框可见时操作

        const currentPeopleCount = this.bookingForm.people.length;
        if (newNum > currentPeopleCount) {
            for (let i = 0; i < newNum - currentPeopleCount; i++) {
                this.bookingForm.people.push({ name: '', tel: '', idCard: '' });
            }
        } else if (newNum < currentPeopleCount) {
            this.bookingForm.people.splice(newNum); // 从末尾移除多余的
        }
    }
  }
};
</script>

<style scoped lang="scss">
// --- SCSS Variables ---
$primary-color: #409EFF;
$warning-color: #E6A23C;
$success-color: #67C23A;
$error-color: #f56c6c; 
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$bg-color-page: #f5f7fa;
$card-bg-light: #ffffff;
$card-shadow: 0 2px 8px rgba(0,0,0,.05);
$card-border-radius: 6px;

// --- Page Container ---
.attraction-detail-page {
  font-family: 'Helvetica Neue', Helvetica, /* ... */;
  background-color: $bg-color-page;
  min-height: calc(100vh - 120px); 
  display: flex;
  flex-direction: column;
}

.attraction-detail-main-content {
  width: 70%; // 详情页内容区可以适当窄一些
  max-width: 960px;
  margin: 25px auto;
  flex-grow: 1;

  .page-loading, .page-empty-detail {
    min-height: 400px; display: flex; flex-direction: column;
    align-items: center; justify-content: center;
    color: $text-color-secondary; font-size: 14px;
    i { font-size: 32px; margin-bottom: 10px; }
    .el-button { margin-top: 15px; }
  }
  .page-loading i { color: $primary-color; animation: rotating 1.5s linear infinite; }
}

.content-wrapper-card { // 包裹所有详情内容的卡片
  background-color: $card-bg-light;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  border: 1px solid $border-color-lighter;
   ::v-deep .el-card__body {
    padding: 20px 25px;
  }
}

// --- 主信息区 (轮播图 + 右侧核心信息) ---
.main-info-row {
  margin-bottom: 25px; // 与下方详情Tabs的间距
}

.carousel-col-detail {
  //
}
.attraction-carousel {
  border-radius: $card-border-radius - 2px;
  overflow: hidden;
  box-shadow: 0 1px 4px rgba(0,0,0,0.07);
  .carousel-image-detail { width: 100%; height: 100%; display: block; }
  .image-slot { /* ... */ }
}
.image-empty-placeholder-detail {
    min-height: 300px; border: 1px dashed $border-color-light; border-radius: $card-border-radius - 2px;
}


.core-info-col {
  display: flex;
  flex-direction: column;
  .attraction-title {
    font-size: 22px; font-weight: 600; color: $text-color-primary;
    margin: 0 0 15px 0; line-height: 1.3;
  }
  .attraction-descriptions {
    margin-bottom: 15px;
    font-size: 13px; // 描述列表字体大小
    ::v-deep .el-descriptions-item__label { width: 80px; color: $text-color-regular; } // 统一标签宽度
    .price-tag { color: #FF4949; font-size: 18px; font-weight: bold; }
  }
  .attraction-introduce-brief {
    font-size: 13px; color: $text-color-regular; line-height: 1.7;
    margin-bottom: 20px;
    // 最多显示3行
    overflow: hidden; text-overflow: ellipsis; display: -webkit-box;
    -webkit-line-clamp: 3; -webkit-box-orient: vertical;
  }
  .book-button-detail {
    width: 100%;
    font-size: 14px; // 按钮字体
    // margin-top: auto; // 如果希望按钮在底部
  }
}

// --- 详细信息 Tabs ---
.detail-info-tabs {
  margin-bottom: 30px; // 与评论区间隔
  border-radius: $card-border-radius - 2px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  border: 1px solid $border-color-lighter; // 给Tabs一个细边框

  ::v-deep .el-tabs__header {
    border-top-left-radius: $card-border-radius - 2px;
    border-top-right-radius: $card-border-radius - 2px;
  }
  ::v-deep .el-tabs__item { font-size: 13px; } // Tab标签字体
  ::v-deep .el-tabs__content { padding: 20px; } // Tab内容区内边距
  
  .info-tab-content {
    font-size: 14px; line-height: 1.7; color: $text-color-regular;
    min-height: 150px; // 给tab内容一个最小高度
  }
}

// --- 评论区 ---
.comments-section {
  .section-title-comment {
    font-size: 18px; font-weight: 500; color: $text-color-primary;
    margin: 0 0 15px 0; padding-bottom: 10px;
    border-bottom: 1px solid $border-color-light;
  }
}
.add-comment-card {
  margin-bottom: 25px;
  border-color: $border-color-lighter; // 评论卡片边框更淡
  ::v-deep .el-card__body { padding: 15px 20px;}
  .el-form-item { margin-bottom: 15px; }
  .comment-rate-item { margin-bottom: 10px; 
    ::v-deep .el-form-item__label { line-height: normal; padding-bottom: 0;} // 评分label对齐
    .el-rate { line-height: 1; margin-top: 4px; }
  }
}

.comments-list-wrapper {
  min-height: 150px; // 评论列表加载时的高度
}
.comments-list {
  .comment-item {
    display: flex;
    padding: 15px 0;
    border-bottom: 1px dashed $border-color-lighter;
    &:last-child { border-bottom: none; }

    .comment-avatar {
      margin-right: 12px;
      flex-shrink: 0;
    }
    .comment-body {
      flex-grow: 1;
      .comment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
        .comment-author { font-size: 14px; color: $text-color-primary; font-weight: 500; }
        .comment-item-rate { ::v-deep .el-rate__icon { font-size: 14px; margin-right: 2px; } ::v-deep .el-rate__text {font-size: 12px;} }
      }
      .comment-content { font-size: 14px; color: $text-color-regular; line-height: 1.6; margin-bottom: 8px; word-wrap: break-word; }
      .comment-footer { font-size: 12px; color: $text-color-secondary; text-align: right; }
    }
  }
}
.comments-pagination { margin-top: 20px; text-align: right; }
.comments-empty { padding: 30px 0; }


// --- 预约对话框 ---
.booking-dialog-detail {
  ::v-deep .el-dialog__header {
    padding: 15px 20px; border-bottom: 1px solid $border-color-light;
    .el-dialog__title { font-size: 16px; font-weight: 500; }
  }
  ::v-deep .el-dialog__body { padding: 20px 25px; }
  .el-form-item { margin-bottom: 18px; 
    &.is-error .el-input__inner { border-color: $error-color; } // 校验失败时输入框边框
  }
  h4 { // 游客信息标题
    font-size: 14px; color: $text-color-primary; margin: 20px 0 10px 0; font-weight: 500;
    border-top: 1px dashed $border-color-lighter; padding-top: 15px;
  }
  .traveler-form-item {
    display: flex;
    align-items: flex-start; // 使校验信息在输入框下方
    gap: 10px; // 表单项之间的横向间距
    margin-bottom: 15px; // 每个游客信息组的下边距
    .el-form-item { margin-bottom: 0 !important; flex: 1; } // 让内部el-form-item占据空间
    .remove-traveler-btn { margin-left: 5px; margin-top: 5px; /* 微调按钮位置 */ }
  }
   .el-alert {
      padding: 6px 12px; // 调整提示框padding
      font-size: 13px;
      margin-bottom: 18px !important; // 确保与下方表单项的间距
    }
}

// --- 富文本 v-html 样式 (与 forumInfo.vue 类似或全局定义) ---
.content-html {
  font-size: 14px; line-height: 1.75; color: $text-color-regular;
  ::v-deep {
    p { margin: 0 0 1em 0; }
    img { max-width: 100% !important; height: auto !important; display: block; margin: 12px auto; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.07); }
    h1,h2,h3,h4,h5,h6 { color: $text-color-primary; margin: 1.6em 0 0.7em 0; font-weight: 600; line-height: 1.4; &:first-child {margin-top: 0;} }
    h1{font-size:1.6em;} h2{font-size:1.4em;} h3{font-size:1.2em;}
    ul, ol { padding-left: 22px; margin-bottom: 1em; li { margin-bottom: 0.5em; line-height: 1.7;} }
    a { color: $primary-color; text-decoration: none; &:hover { text-decoration: underline; } }
    blockquote { margin: 1.2em 0; padding: 8px 12px; border-left: 3px solid $primary-color; background-color: #f8f9fa; p{margin-bottom:0.4em; &:last-child{margin-bottom:0;}} }
    table { width:auto; border-collapse:collapse; margin:1.2em 0; font-size:0.9em; th,td{border:1px solid $border-color-light; padding:6px 8px; text-align:left;} th{background-color:#fafcff; font-weight:500;} }
    pre, code { font-family: Menlo, Monaco, Consolas, "Courier New", monospace; background-color:#f6f8fa; border-radius:3px; padding:0.2em 0.4em; font-size:0.9em; }
    pre { padding: 0.8em; overflow-x: auto; }
  }
}

@keyframes rotating { /* ... */ }
</style>