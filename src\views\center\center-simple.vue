<template>
  <div class="center-page">
    <header-page />
    
    <div class="center-content">
      <el-card class="profile-card">
        <div class="profile-header">
          <h2>个人中心</h2>
        </div>
        
        <el-row :gutter="20">
          <!-- 用户信息表单 -->
          <el-col :xs="24" :sm="14" :md="15">
            <el-form :model="userForm" label-width="100px" class="user-form">
              <el-form-item label="用户名">
                <el-input v-model="userForm.username" disabled></el-input>
              </el-form-item>
              <el-form-item label="昵称">
                <el-input v-model="userForm.name"></el-input>
              </el-form-item>
              <el-form-item label="邮箱">
                <el-input v-model="userForm.email"></el-input>
              </el-form-item>
              <el-form-item label="手机号">
                <el-input v-model="userForm.phone"></el-input>
              </el-form-item>
              <el-form-item label="性别">
                <el-radio-group v-model="userForm.sex">
                  <el-radio label="1">男</el-radio>
                  <el-radio label="0">女</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="saveUserInfo">保存信息</el-button>
                <el-button @click="openPasswordDialog">修改密码</el-button>
              </el-form-item>
            </el-form>
          </el-col>
          
          <!-- 头像区域 -->
          <el-col :xs="24" :sm="10" :md="9">
            <div class="avatar-section">
              <div class="avatar-container">
                <el-image
                  class="user-avatar"
                  :src="avatarUrl"
                  fit="cover"
                >
                  <div slot="error" class="avatar-error">
                    <i class="el-icon-picture-outline"></i>
                  </div>
                </el-image>
              </div>
              
              <el-upload
                class="avatar-uploader"
                :action="uploadUrl"
                :show-file-list="false"
                :before-upload="beforeAvatarUpload"
                :on-success="handleAvatarSuccess"
                :on-error="handleAvatarError"
                accept="image/*"
              >
                <el-button size="small" icon="el-icon-upload2">修改头像</el-button>
              </el-upload>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>
    
    <!-- 修改密码对话框 -->
    <el-dialog title="修改密码" :visible.sync="passwordVisible" width="400px">
      <el-form :model="passwordForm" label-width="80px">
        <el-form-item label="原密码">
          <el-input v-model="passwordForm.oldPassword" type="password"></el-input>
        </el-form-item>
        <el-form-item label="新密码">
          <el-input v-model="passwordForm.newPassword" type="password"></el-input>
        </el-form-item>
        <el-form-item label="确认密码">
          <el-input v-model="passwordForm.confirmPassword" type="password"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="passwordVisible = false">取消</el-button>
        <el-button type="primary" @click="changeUserPassword">确定</el-button>
      </div>
    </el-dialog>
    
    <bottom-page />
  </div>
</template>

<script>
import headerPage from "../../components/header";
import bottomPage from "../../components/bottom";
import { getUser, setUserInfo, changePassword } from '../../api/api';

export default {
  name: 'UserCenter',
  components: {
    headerPage,
    bottomPage,
  },
  data() {
    return {
      userForm: {
        id: '',
        username: '',
        name: '',
        email: '',
        phone: '',
        sex: '1',
        avatar: ''
      },
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordVisible: false
    };
  },
  computed: {
    avatarUrl() {
      if (!this.userForm.avatar) {
        return '';
      }

      const avatar = this.userForm.avatar;

      // 如果头像路径已经是完整的HTTP URL，直接返回
      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
        return avatar;
      }

      // 如果头像路径是相对路径（以/开头），拼接HOST
      if (avatar.startsWith('/')) {
        return (this.$store.state.HOST || '') + avatar;
      }

      // 其他情况，也拼接HOST（兼容不以/开头的相对路径）
      return (this.$store.state.HOST || '') + '/' + avatar;
    },
    uploadUrl() {
      return this.userForm.id ? 
        `${this.$store.state.HOST}/user/setUserAvatar/${this.userForm.id}` : 
        `${this.$store.state.HOST}/user/setUserAvatar/temp`;
    }
  },
  methods: {
    // 获取用户信息
    async fetchUserInfo() {
      try {
        const res = await getUser();
        if (res && res.code === 1000 && res.data) {
          this.userForm = { ...this.userForm, ...res.data };
          console.log('用户信息:', this.userForm);
        } else {
          this.$message.error(res.message || '获取用户信息失败');
        }
      } catch (error) {
        console.error('获取用户信息异常:', error);
        this.$message.error('网络错误，获取用户信息失败');
      }
    },
    
    // 保存用户信息
    async saveUserInfo() {
      try {
        const res = await setUserInfo(this.userForm);
        if (res && res.code === 1000) {
          this.$message.success('保存成功');
        } else {
          this.$message.error(res.message || '保存失败');
        }
      } catch (error) {
        console.error('保存用户信息异常:', error);
        this.$message.error('网络错误，保存失败');
      }
    },
    
    // 头像上传前验证
    beforeAvatarUpload(file) {
      const isImage = file.type.startsWith('image/');
      const isLt20M = file.size / 1024 / 1024 < 20;
      
      if (!isImage) {
        this.$message.error('只能上传图片文件!');
        return false;
      }
      if (!isLt20M) {
        this.$message.error('图片大小不能超过 20MB!');
        return false;
      }
      
      console.log('上传URL:', this.uploadUrl);
      console.log('用户ID:', this.userForm.id);
      return true;
    },
    
    // 头像上传成功
    handleAvatarSuccess(res) {
      console.log('上传响应:', res);
      if (res && res.code === 1000) {
        this.$message.success('头像上传成功!');
        this.userForm.avatar = res.data;

        // 更新localStorage中的用户信息
        this.updateLocalStorageUserInfo();

        // 触发导航栏更新
        this.triggerHeaderRefresh();

        // 重新获取用户信息
        this.fetchUserInfo();
      } else {
        this.$message.error(res.message || '头像上传失败');
      }
    },

    // 更新localStorage中的用户信息
    updateLocalStorageUserInfo() {
      try {
        const userInfoStr = window.localStorage.getItem("user_info");
        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          userInfo.avatar = this.userForm.avatar;
          window.localStorage.setItem("user_info", JSON.stringify(userInfo));
          console.log('已更新localStorage中的用户头像信息');
        }
      } catch (error) {
        console.error('更新localStorage用户信息失败:', error);
      }
    },

    // 触发导航栏刷新
    triggerHeaderRefresh() {
      // 使用事件总线通知导航栏更新
      if (this.$bus) {
        this.$bus.$emit('avatarUpdated', this.userForm.avatar);
        this.$bus.$emit('userInfoUpdated', this.userForm);
        this.$bus.$emit('refreshUserInfo');
      }

      console.log('已触发导航栏刷新事件');
    },
    
    // 头像上传失败
    handleAvatarError(err) {
      console.error('头像上传错误:', err);
      this.$message.error('头像上传失败，请检查网络或联系管理员');
    },
    
    // 打开修改密码对话框
    openPasswordDialog() {
      this.passwordVisible = true;
    },
    
    // 修改密码
    async changeUserPassword() {
      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
        this.$message.error('两次输入的密码不一致');
        return;
      }
      
      try {
        const params = {
          id: this.userForm.id,
          password: this.passwordForm.oldPassword,
          newPassword: this.passwordForm.newPassword
        };
        const res = await changePassword(params);
        if (res && res.code === 1000) {
          this.$message.success('密码修改成功');
          this.passwordVisible = false;
          this.passwordForm = {
            oldPassword: '',
            newPassword: '',
            confirmPassword: ''
          };
        } else {
          this.$message.error(res.message || '密码修改失败');
        }
      } catch (error) {
        console.error('修改密码异常:', error);
        this.$message.error('网络错误，密码修改失败');
      }
    }
  },
  created() {
    this.fetchUserInfo();
  }
};
</script>

<style scoped>
.center-page {
  min-height: 100vh;
  background-color: #f5f7fa;
}

.center-content {
  padding: 20px;
  display: flex;
  justify-content: center;
}

.profile-card {
  width: 100%;
  max-width: 1000px;
}

.profile-header {
  text-align: center;
  margin-bottom: 30px;
}

.profile-header h2 {
  color: #303133;
  margin: 0;
}

.user-form {
  padding-right: 20px;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-left: 20px;
}

.avatar-container {
  margin-bottom: 20px;
}

.user-avatar {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 3px solid #fff;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.avatar-error {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 24px;
}

.avatar-uploader {
  text-align: center;
}

@media (max-width: 768px) {
  .user-form {
    padding-right: 0;
    margin-bottom: 20px;
  }
  
  .avatar-section {
    padding-left: 0;
  }
}
</style>
