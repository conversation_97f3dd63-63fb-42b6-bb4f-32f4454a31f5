<template>
  <div class="user-center-page">
    <header-page /> 
    <div class="user-center-content">
      <el-card class="profile-card" shadow="hover">
        <el-row :gutter="40"> 
          <el-col :xs="24" :sm="14" :md="15" class="profile-form-col">
            <el-form :model="userForm" :rules="formRules" ref="userFormRef" label-width="130px" class="profile-form">
              <el-form-item label="登录账号" prop="loginAccount">
                <el-input size="small" v-model="userForm.loginAccount" disabled />
              </el-form-item>
              <el-form-item label="用户名" prop="userName">
                <el-input size="small" v-model="userForm.userName" placeholder="请输入用户名" />
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input size="small" v-model="userForm.email" placeholder="请输入邮箱" />
              </el-form-item>
              <el-form-item label="联系电话" prop="tel">
                <el-input size="small" v-model="userForm.tel" placeholder="请输入联系电话" />
              </el-form-item>
              <el-form-item label="性别" prop="sex">
                <el-radio-group v-model="userForm.sex">
                  <el-radio label="0">男</el-radio>
                  <el-radio label="1">女</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="上次修改密码时间" prop="pwdUpdateDate">
                <el-input size="small" v-model="userForm.pwdUpdateDate" disabled />
              </el-form-item>
              <el-form-item class="form-actions">
                <el-button type="primary" size="small" @click="submitUserInfo" icon="el-icon-check">保存信息</el-button>
              </el-form-item>
            </el-form>
          </el-col>

          <el-col :xs="24" :sm="10" :md="9" class="profile-avatar-col">
            <div class="avatar-section">
              <el-image
                class="user-avatar"
                :src="avatarDisplayUrl"
                :preview-src-list="[avatarDisplayUrl]" 
                fit="cover"
              >
                <div slot="error" class="avatar-error-slot">
                  <i class="el-icon-picture-outline"></i>
                </div>
                <div slot="placeholder" class="avatar-placeholder-slot">
                  <i class="el-icon-loading"></i>
                </div>
              </el-image>
              <el-upload
                class="avatar-uploader"
                ref="avatarUploadRef"
                :action="avatarUploadActionUrl"
                :show-file-list="false"
                :before-upload="beforeAvatarUpload"
                :on-success="handleAvatarSuccess"
                :on-error="handleAvatarError"
                accept="image/png, image/jpeg, image/gif"
              >
                <el-button size="small" icon="el-icon-upload2">修改头像</el-button>
              </el-upload>
            </div>
            <div class="password-section">
              <el-button size="small" icon="el-icon-key" @click="openPasswordDialog" class="change-password-btn">
                修改密码
              </el-button>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <el-dialog
      title="修改密码"
      :visible.sync="passwordDialog.visible"
      width="450px" 
      :before-close="handlePasswordDialogClose"
      class="password-dialog"
      append-to-body
      destroy-on-close
    >
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordFormRef" label-width="90px" @submit.native.prevent="submitPasswordChange">
        <div class="password-dialog-tip">正在为用户 <span class="username-highlight">{{ userForm.userName }}</span> 修改密码</div>
        <el-form-item label="旧密码" prop="oldPassword">
          <el-input type="password" v-model="passwordForm.oldPassword" size="small" show-password autocomplete="new-password" placeholder="请输入旧密码"/>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input type="password" v-model="passwordForm.newPassword" size="small" show-password autocomplete="new-password" placeholder="请输入新密码"/>
        </el-form-item>
         <el-form-item label="确认密码" prop="confirmPassword">
          <el-input type="password" v-model="passwordForm.confirmPassword" size="small" show-password autocomplete="new-password" placeholder="请再次输入新密码"/>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handlePasswordDialogClose">取 消</el-button>
        <el-button size="small" type="primary" @click="submitPasswordChange" :loading="passwordDialog.loading">确 定</el-button>
      </span>
    </el-dialog>

    <bottom-page /> {/* 保持驼峰或短横线命名一致 */}
  </div>
</template>
<script>
import { mixin } from "../../minix"; // 假设 mixin 已正确配置
import headerPage from "../../components/header";
import bottomPage from "../../components/bottom";
import { getUser, setUserInfo, setUserAvatar, changePassword } from '../../api/api'; // 确保 API 路径和函数名正确

// 从 Vuex store 或环境变量获取上传基础URL (更推荐的方式)
const UPLOAD_BASE_URL = process.env.VUE_APP_BASE_API; // 示例

export default {
  name: 'UserProfileCenter',
  mixins: [mixin], // 谨慎使用 mixin，确保了解其作用和潜在冲突
  components: {
    headerPage,
    bottomPage,
  },
  data() {
    // 密码校验规则
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'));
      } else if (value.length < 6) {
        callback(new Error('密码长度不能少于6位'));
      } else {
        if (this.passwordForm.confirmPassword !== '') {
          this.$refs.passwordFormRef.validateField('confirmPassword');
        }
        callback();
      }
    };
    const validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    };
    // 手机号校验规则
    const checkPhone = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请输入联系电话'));
      }
      const reg = /^1[3-9]\d{9}$/; // 更新手机号正则
      if (reg.test(value)) {
        callback();
      } else {
        return callback(new Error('请输入正确的11位手机号码'));
      }
    };

    return {
      userForm: { // 表单数据对象
        id: null,
        loginAccount: '',
        userName: '',
        email: '',
        tel: '',
        sex: '0', // 默认值
        avatar: '',
        pwdUpdateDate: '',
      },
      // avatarPreviewList: [], // 用于 el-image 的 preview-src-list
      
      formRules: { // 表单校验规则
        userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: ['blur', 'change'] },
        ],
        tel: [{ required: true, validator: checkPhone, trigger: 'blur' }],
        sex: [{ required: true, message: '请选择性别', trigger: 'change' }],
      },

      // 修改密码对话框相关
      passwordDialog: {
        visible: false,
        loading: false,
      },
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: '',
      },
      passwordRules: {
        oldPassword: [{ required: true, message: '请输入旧密码', trigger: 'blur' }],
        newPassword: [
          { required: true, validator: validatePass, trigger: 'blur' },
        ],
        confirmPassword: [
          { required: true, validator: validatePass2, trigger: 'blur' },
        ],
      },
    };
  },
  computed: {
    avatarDisplayUrl() {
      if (!this.userForm.avatar) {
        return '';
      }

      const avatar = this.userForm.avatar;

      // 如果头像路径已经是完整的HTTP URL，直接返回
      if (avatar.startsWith('http://') || avatar.startsWith('https://')) {
        return avatar;
      }

      // 如果头像路径是相对路径（以/开头），拼接HOST
      if (avatar.startsWith('/')) {
        return (this.$store.state.HOST || '') + avatar;
      }

      // 其他情况，也拼接HOST（兼容不以/开头的相对路径）
      return (this.$store.state.HOST || '') + '/' + avatar;
    },
    avatarUploadActionUrl() {
      // 构建完整的上传URL，使用正确的后端接口路径
      return this.userForm.id ? `${this.$store.state.HOST}/user/setUserAvatar/${this.userForm.id}` : `${this.$store.state.HOST}/user/setUserAvatar/temp`;
    }
  },
  methods: {
    // --- 用户信息 ---
    async fetchUserInfo() {
      try {
        const res = await getUser(); // 假设 getUser 不需要参数，或从store获取用户ID
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.userForm = { ...this.userForm, ...res.data, sex: String(res.data.sex) }; // 合并数据，并确保sex是字符串
          // this.avatarPreviewList = [this.avatarDisplayUrl];
        } else {
          this.$message.error(res.message || '获取用户信息失败');
        }
      } catch (error) {
        console.error('获取用户信息异常:', error);
        this.$message.error('网络错误，获取用户信息失败');
      }
    },
    submitUserInfo() {
      this.$refs.userFormRef.validate(async (valid) => {
        if (valid) {
          try {
            // 准备提交的数据，避免提交不必要的字段
            const dataToSubmit = {
              id: this.userForm.id,
              userName: this.userForm.userName,
              email: this.userForm.email,
              tel: this.userForm.tel,
              sex: this.userForm.sex,
            };
            const res = await setUserInfo(dataToSubmit);
            if (res && (res.code === 1000 || res.code === 200)) {
              this.$message.success('用户信息保存成功!');
              this.fetchUserInfo(); // 重新获取信息以同步显示
            } else {
              this.$message.error(res.message || '保存失败');
            }
          } catch (error) {
            console.error('保存用户信息异常:', error);
            this.$message.error('网络错误，保存失败');
          }
        } else {
          console.log('表单校验失败!');
          return false;
        }
      });
    },

    // --- 头像上传 ---
    beforeAvatarUpload(file) {
      console.log('🔍 上传文件信息:', {
        name: file.name,
        type: file.type,
        size: file.size,
        sizeInMB: (file.size / 1024 / 1024).toFixed(2)
      });

      console.log('🔍 上传URL:', this.avatarUploadActionUrl);
      console.log('🔍 用户ID:', this.userForm.id);
      console.log('🔍 HOST配置:', this.$store.state.HOST);

      // 修复文件类型验证逻辑
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      const isIMAGE = allowedTypes.includes(file.type);
      const isLt20M = file.size / 1024 / 1024 < 20;

      if (!isIMAGE) {
        this.$message.error('上传文件只能是图片格式! 支持 JPG、PNG、GIF 格式');
        console.log('❌ 文件类型不支持:', file.type);
        return false;
      }
      if (!isLt20M) {
        this.$message.error('上传图片大小不能超过 20MB!');
        console.log('❌ 文件过大:', (file.size / 1024 / 1024).toFixed(2) + 'MB');
        return false;
      }

      console.log('✅ 文件验证通过，准备上传');
      return true;
    },
    handleAvatarSuccess(res, file) {
      console.log('🔍 头像上传响应:', res);
      console.log('🔍 上传文件信息:', file);

      // 兼容不同的后端响应格式
      if (res && res.code === 1000) {
        // 头像路径可能在 data 或 message 字段中
        const avatarPath = res.data || res.message;

        if (avatarPath) {
          this.$message.success('头像上传成功!');

          // 更新用户表单中的头像路径
          this.userForm.avatar = avatarPath;
          console.log('✅ 更新头像路径:', avatarPath);
          console.log('✅ 完整头像URL:', this.avatarDisplayUrl);

          // 更新localStorage中的用户信息
          this.updateLocalStorageUserInfo();

          // 触发导航栏更新
          this.triggerHeaderRefresh();

          // 重新获取用户信息以确保数据同步
          this.fetchUserInfo();
        } else {
          console.error('❌ 响应中没有头像路径:', res);
          this.$message.error('头像上传失败：服务器响应异常');
        }
      } else {
        console.error('❌ 头像上传失败:', res);
        this.$message.error(res.message || '头像上传失败');
      }
    },

    // 更新localStorage中的用户信息
    updateLocalStorageUserInfo() {
      try {
        const userInfoStr = window.localStorage.getItem("user_info");
        if (userInfoStr) {
          const userInfo = JSON.parse(userInfoStr);
          userInfo.avatar = this.userForm.avatar;
          window.localStorage.setItem("user_info", JSON.stringify(userInfo));
          console.log('✅ 已更新localStorage中的用户头像信息');
        }
      } catch (error) {
        console.error('❌ 更新localStorage用户信息失败:', error);
      }
    },

    // 触发导航栏刷新
    triggerHeaderRefresh() {
      // 使用事件总线通知导航栏更新
      if (this.$bus) {
        this.$bus.$emit('avatarUpdated', this.userForm.avatar);
        this.$bus.$emit('userInfoUpdated', this.userForm);
        this.$bus.$emit('refreshUserInfo');
        console.log('✅ 已触发导航栏刷新事件');
      } else {
        console.warn('⚠️ 事件总线不可用');
      }
    },
    handleAvatarError(err) {
      console.error('❌ 头像上传错误:', err);

      // 根据错误类型提供更具体的错误信息
      let errorMessage = '头像上传失败';

      if (err.status === 413) {
        errorMessage = '文件过大，请选择小于20MB的图片';
      } else if (err.status === 415) {
        errorMessage = '不支持的文件格式，请选择JPG、PNG或GIF格式';
      } else if (err.status === 500) {
        errorMessage = '服务器错误，请稍后重试';
      } else if (err.status === 0) {
        errorMessage = '网络连接失败，请检查网络';
      } else {
        errorMessage = `上传失败: ${err.message || '未知错误'}`;
      }

      this.$message.error(errorMessage);
    },

    // --- 修改密码 ---
    openPasswordDialog() {
      this.passwordDialog.visible = true;
      // this.$bus.$emit('password', true); // 如果仍需事件总线
    },
    handlePasswordDialogClose() {
      this.$refs.passwordFormRef.resetFields(); // 关闭时重置表单
      this.passwordDialog.visible = false;
      // this.$bus.$emit('password', false);
    },
    submitPasswordChange() {
      this.$refs.passwordFormRef.validate(async (valid) => {
        if (valid) {
          this.passwordDialog.loading = true;
          try {
            const params = {
              id: this.userForm.id,
              password: this.passwordForm.oldPassword, // 注意：这里用的是 password，后端接口可能叫 oldPassword
              newPassword: this.passwordForm.newPassword,
            };
            const res = await changePassword(params);
            if (res && (res.code === 1000 || res.code === 200)) {
              this.$notify.success({ title: '成功', message: '密码修改成功，请重新登录' });
              this.handlePasswordDialogClose();
              // 密码修改成功后通常需要用户重新登录
              // await this.$store.dispatch('user/logout'); // 示例：调用Vuex的登出action
              // this.$router.push('/login');
            } else {
              this.$message.error(res.message || '密码修改失败');
            }
          } catch (error) {
            console.error('修改密码异常:', error);
            this.$message.error('网络错误，密码修改失败');
          } finally {
            this.passwordDialog.loading = false;
          }
        } else {
          return false;
        }
      });
    },
  },
  created() {
    this.fetchUserInfo();
  },
  mounted() {
    // 统一在 passwordDialog.visible 控制显示，如果还用事件总线，则在此处监听
    // this.$bus.$on('password', (isVisible) => {
    //   this.passwordDialog.visible = isVisible;
    //   if (!isVisible) {
    //     this.$refs.passwordFormRef && this.$refs.passwordFormRef.resetFields();
    //   }
    // });
  },
};
</script>
<style scoped lang="scss">
// --- SCSS Variables ---
$primary-color: #409EFF;
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399; // <--- 确保这行或类似的定义存在
$border-color-light: #e4e7ed;
$bg-color-page: #f5f7fa;
$card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
$card-border-radius: 8px;
// --- Page Container ---
.user-center-page {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  background-color: $bg-color-page;
  min-height: calc(100vh - 120px); // 假设 header 和 bottom 各占 60px
  padding-bottom: 20px; // 底部留白
}

.user-center-content {
  display: flex;
  justify-content: center;
  padding-top: 20px; // 卡片与头部导航的间距
}

.profile-card {
  width: 75%; // 卡片宽度调整
  max-width: 1000px; // 最大宽度
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  
  ::v-deep .el-card__body {
    padding: 30px 35px; // 卡片内部padding加大
  }
}

// --- Form Area ---
.profile-form-col {
  border-right: 1px solid $border-color-light; // 右边框作为分割线
  padding-right: 35px !important; // Element UI gutter 会被覆盖，用!important或更具体选择器
  
  @media (max-width: 768px) { // 小屏幕下
    border-right: none;
    border-bottom: 1px solid $border-color-light;
    padding-right: 10px !important; // Element UI :xs="24" gutter会是10px, 这里保持一致
    padding-bottom: 20px;
    margin-bottom: 20px;
  }
}

.profile-form {
  .el-form-item {
    margin-bottom: 18px; // 表单项间距
  }
  .el-input--small .el-input__inner,
  .el-radio-group {
    font-size: 13px; // 输入框和单选框字体
  }
  .form-actions {
    margin-top: 25px; // 保存按钮与上方表单项的间距
    text-align: center; // 按钮居中 (如果label-width不是100%)
    ::v-deep .el-form-item__content { // Element UI 按钮默认在label后，如需居中
        margin-left: 0 !important; // 如果希望按钮占满一行并居中，可以移除 label-width 的影响
        // display: flex;
        // justify-content: center;
    }
  }
}


// --- Avatar Area ---
.profile-avatar-col {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; // 从顶部开始对齐
  padding-left: 35px !important; // 左侧内边距

  @media (max-width: 768px) {
    padding-left: 10px !important;
  }
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-bottom: 25px; // 头像区域与密码按钮的间距

  .user-avatar {
    width: 180px; // 头像尺寸调整
    height: 180px;
    border-radius: 50%;
    overflow: hidden;
    border: 3px solid #fff; // 白色边框
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 15px; // 头像与上传按钮的间距
    background-color: #f0f2f5; // 头像背景色

    .avatar-error-slot, .avatar-placeholder-slot {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      height: 100%;
      background: #f5f7fa;
      color: $text-color-secondary;
      font-size: 28px; // 图标放大
    }
  }
  .avatar-uploader {
    text-align: center;
  }
}

.password-section {
  width: 100%;
  display: flex;
  justify-content: center; // 按钮居中

}


// --- Password Dialog ---
.password-dialog {
  ::v-deep .el-dialog__body {
    padding: 20px 25px; // 对话框内容区内边距
  }
  .password-dialog-tip {
    font-size: 13px;
    color: $text-color-regular;
    margin-bottom: 15px;
    .username-highlight {
      font-weight: bold;
      color: $primary-color;
    }
  }
  .el-form-item {
    margin-bottom: 18px;
  }
  .dialog-footer { // Element UI 对话框底部默认样式即可，无需额外样式
    // text-align: right;
  }
}

// 移除您原有的 .centerPage, .content, .master, .slave 等类的固定高度和宽度百分比，
// 使用 Element UI Grid 和 Flexbox 进行布局。
// .centerPage, .centerPage-content 等基础布局由 Element UI Grid 处理。
// .box-card 的宽度通过 profile-card 类调整。
</style>
