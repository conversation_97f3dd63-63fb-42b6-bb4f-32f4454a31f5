<template>
  <div class="debug-booking-page">
    <headers />
    
    <div class="debug-content">
      <h1 class="page-title">🔧 预约功能完整调试工具</h1>
      
      <!-- 步骤1：检查登录状态 -->
      <section class="debug-section">
        <h2 class="section-title">步骤1：检查登录状态</h2>
        <div class="status-check">
          <div class="check-item" :class="{ 'success': hasToken, 'error': !hasToken }">
            <i :class="hasToken ? 'el-icon-check' : 'el-icon-close'"></i>
            <span>Token状态: {{ hasToken ? '存在' : '缺失' }}</span>
            <div v-if="hasToken" class="token-info">
              <small>Token: {{ userToken.substring(0, 20) }}...</small>
            </div>
          </div>
          
          <div class="check-item" :class="{ 'success': hasUserInfo, 'error': !hasUserInfo }">
            <i :class="hasUserInfo ? 'el-icon-check' : 'el-icon-close'"></i>
            <span>用户信息: {{ hasUserInfo ? '存在' : '缺失' }}</span>
            <div v-if="hasUserInfo" class="user-info">
              <small>用户名: {{ userInfo.user_name || '未设置' }}</small>
            </div>
          </div>
          
          <div class="action-buttons">
            <el-button @click="checkLogin" type="primary" :loading="checking">重新检查</el-button>
            <el-button @click="forceLogin" type="warning">强制登录</el-button>
            <el-button @click="clearAll" type="danger">清除所有</el-button>
          </div>
        </div>
      </section>

      <!-- 步骤2：测试Token有效性 -->
      <section class="debug-section">
        <h2 class="section-title">步骤2：测试Token有效性</h2>
        <div class="token-test">
          <el-button @click="testTokenValidity" type="success" :loading="tokenTesting">
            测试Token有效性
          </el-button>
          
          <div v-if="tokenTestResult" class="test-result">
            <h4>Token测试结果:</h4>
            <pre class="code-block">{{ JSON.stringify(tokenTestResult, null, 2) }}</pre>
          </div>
        </div>
      </section>

      <!-- 步骤3：测试预约API -->
      <section class="debug-section">
        <h2 class="section-title">步骤3：测试预约API</h2>
        <div class="booking-test">
          <div class="test-params">
            <h4>测试参数:</h4>
            <el-form :model="testParams" label-width="120px">
              <el-form-item label="景点ID">
                <el-input v-model="testParams.attractionsId"></el-input>
              </el-form-item>
              <el-form-item label="预约人数">
                <el-input-number v-model="testParams.num" :min="1"></el-input-number>
              </el-form-item>
              <el-form-item label="预约时间">
                <el-date-picker
                  v-model="testParams.time"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="选择日期">
                </el-date-picker>
              </el-form-item>
            </el-form>
          </div>
          
          <el-button @click="testBookingAPI" type="info" :loading="bookingTesting">
            测试预约API
          </el-button>
          
          <div v-if="bookingTestResult" class="test-result">
            <h4>预约API测试结果:</h4>
            <pre class="code-block">{{ JSON.stringify(bookingTestResult, null, 2) }}</pre>
          </div>
        </div>
      </section>

      <!-- 步骤4：网络请求监控 -->
      <section class="debug-section">
        <h2 class="section-title">步骤4：网络请求监控</h2>
        <div class="network-monitor">
          <p>请打开浏览器开发者工具的Network标签页，然后点击下面的按钮发送测试请求：</p>
          <el-button @click="sendTestRequest" type="primary">发送测试请求</el-button>
          
          <div class="monitor-tips">
            <h4>检查要点:</h4>
            <ul>
              <li>✅ 请求头中是否包含 <code>x_access_token</code></li>
              <li>✅ Token值是否正确</li>
              <li>✅ 请求参数是否完整</li>
              <li>✅ 响应状态码是否为200</li>
              <li>✅ 响应数据是否正确</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- 步骤5：时间同步检查 -->
      <section class="debug-section">
        <h2 class="section-title">步骤5：时间同步检查</h2>
        <div class="time-check">
          <div class="time-info">
            <p><strong>本地时间:</strong> {{ localTime }}</p>
            <p><strong>UTC时间:</strong> {{ utcTime }}</p>
            <p><strong>时间戳:</strong> {{ timestamp }}</p>
            <p><strong>格式化时间:</strong> {{ formattedTime }}</p>
          </div>
          
          <el-button @click="refreshTime" type="primary">刷新时间</el-button>
          
          <div class="time-tips">
            <h4>时间问题排查:</h4>
            <ul>
              <li>检查本地时间是否正确</li>
              <li>检查服务器时区设置</li>
              <li>确认数据库时区配置</li>
            </ul>
          </div>
        </div>
      </section>

      <!-- 诊断结果 -->
      <section class="debug-section">
        <h2 class="section-title">诊断结果和建议</h2>
        <div class="diagnosis">
          <div v-if="!hasToken" class="issue error">
            <h4>❌ 严重问题：缺少Token</h4>
            <p>系统中没有找到用户Token，这是导致创建者显示为"游客"的主要原因。</p>
            <p><strong>解决方案：</strong></p>
            <ol>
              <li>点击"强制登录"按钮重新登录</li>
              <li>确保登录成功后有Token保存</li>
              <li>重新测试预约功能</li>
            </ol>
          </div>
          
          <div v-else-if="!hasUserInfo" class="issue warning">
            <h4>⚠️ 警告：缺少用户信息</h4>
            <p>有Token但缺少用户信息，可能导致用户名显示异常。</p>
            <p><strong>解决方案：</strong>重新登录获取完整用户信息</p>
          </div>
          
          <div v-else-if="tokenTestResult && tokenTestResult.code !== 200" class="issue error">
            <h4>❌ Token无效</h4>
            <p>Token存在但已失效，后端无法验证用户身份。</p>
            <p><strong>解决方案：</strong>重新登录获取新的Token</p>
          </div>
          
          <div v-else class="issue success">
            <h4>✅ 基础检查通过</h4>
            <p>Token和用户信息都存在，如果仍有问题，可能是后端处理逻辑问题。</p>
            <p><strong>建议：</strong>检查后端日志和数据库记录</p>
          </div>
        </div>
      </section>
    </div>
    
    <bottoms />
  </div>
</template>

<script>
import headers from '@/components/header'
import bottoms from '@/components/bottom'
import { getUser, saveSysAttractionOrder } from '@/api/api'

export default {
  name: 'DebugBooking',
  components: {
    headers,
    bottoms
  },
  data() {
    return {
      checking: false,
      tokenTesting: false,
      bookingTesting: false,
      tokenTestResult: null,
      bookingTestResult: null,
      localTime: '',
      utcTime: '',
      timestamp: '',
      formattedTime: '',
      testParams: {
        attractionsId: '1',
        num: 1,
        time: '2024-12-25'
      }
    }
  },
  computed: {
    userToken() {
      return localStorage.getItem('user_token') || '';
    },
    userInfo() {
      const info = localStorage.getItem('user_info');
      try {
        return info ? JSON.parse(info) : {};
      } catch (e) {
        return {};
      }
    },
    hasToken() {
      return !!this.userToken;
    },
    hasUserInfo() {
      return !!localStorage.getItem('user_info') && !!this.userInfo.id;
    }
  },
  methods: {
    checkLogin() {
      this.checking = true;
      setTimeout(() => {
        this.$forceUpdate();
        this.checking = false;
        this.$message.info('登录状态已刷新');
      }, 500);
    },
    
    forceLogin() {
      // 创建一个有效的测试Token和用户信息
      const testToken = 'test_token_' + Date.now();
      const testUser = {
        id: '0de96461b6ef0328cef416dea9366c9c',
        user_name: '测试用户_' + new Date().getHours() + new Date().getMinutes(),
        login_account: 'test_user',
        email: '<EMAIL>',
        tel: '***********'
      };
      
      localStorage.setItem('user_token', testToken);
      localStorage.setItem('user_info', JSON.stringify(testUser));
      
      this.$message.success('强制登录成功，已创建测试用户');
      this.$forceUpdate();
    },
    
    clearAll() {
      localStorage.removeItem('user_token');
      localStorage.removeItem('user_info');
      this.$message.info('所有登录信息已清除');
      this.$forceUpdate();
    },
    
    async testTokenValidity() {
      this.tokenTesting = true;
      this.tokenTestResult = null;
      
      try {
        console.log('🧪 开始测试Token有效性...');
        console.log('📋 当前Token:', this.userToken);
        
        const result = await getUser();
        console.log('📥 Token测试结果:', result);
        
        this.tokenTestResult = result;
        
        if (result && (result.code === 200 || result.code === 1000)) {
          this.$message.success('Token有效，用户信息获取成功');
        } else {
          this.$message.error('Token无效: ' + (result.message || '未知错误'));
        }
      } catch (error) {
        console.error('❌ Token测试失败:', error);
        this.tokenTestResult = { error: error.message };
        this.$message.error('Token测试失败: ' + error.message);
      } finally {
        this.tokenTesting = false;
      }
    },
    
    async testBookingAPI() {
      this.bookingTesting = true;
      this.bookingTestResult = null;
      
      try {
        console.log('🧪 开始测试预约API...');
        
        const params = {
          attractionsId: this.testParams.attractionsId,
          num: this.testParams.num,
          time: this.testParams.time,
          people: JSON.stringify([{
            name: '测试用户',
            tel: '***********',
            idCard: '110101199001011234'
          }]),
          userId: this.userInfo.id || '1'
        };
        
        console.log('📋 预约API参数:', params);
        
        const result = await saveSysAttractionOrder(params);
        console.log('📥 预约API结果:', result);
        
        this.bookingTestResult = result;
        
        if (result && (result.code === 200 || result.code === 1000)) {
          this.$message.success('预约API测试成功');
        } else {
          this.$message.error('预约API测试失败: ' + (result.message || '未知错误'));
        }
      } catch (error) {
        console.error('❌ 预约API测试失败:', error);
        this.bookingTestResult = { error: error.message };
        this.$message.error('预约API测试失败: ' + error.message);
      } finally {
        this.bookingTesting = false;
      }
    },
    
    sendTestRequest() {
      console.log('📡 发送测试请求，请查看Network标签页');
      this.testTokenValidity();
    },
    
    refreshTime() {
      const now = new Date();
      this.localTime = now.toLocaleString();
      this.utcTime = now.toUTCString();
      this.timestamp = now.getTime();
      this.formattedTime = now.toISOString().slice(0, 19).replace('T', ' ');
    }
  },
  
  mounted() {
    this.refreshTime();
    console.log('🔧 预约功能调试工具已加载');
    console.log('📍 当前Token:', this.userToken);
    console.log('👤 当前用户信息:', this.userInfo);
  }
}
</script>

<style scoped lang="scss">
.debug-booking-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.debug-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.page-title {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.debug-section {
  margin-bottom: 40px;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.check-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 12px;
  border-radius: 8px;
  
  &.success {
    background: #ecfdf5;
    color: #059669;
  }
  
  &.error {
    background: #fef2f2;
    color: #dc2626;
  }
  
  i {
    margin-right: 8px;
    font-size: 16px;
  }
  
  .token-info, .user-info {
    margin-left: auto;
    font-size: 12px;
    opacity: 0.8;
  }
}

.action-buttons {
  margin-top: 16px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.test-result {
  margin-top: 16px;
  
  h4 {
    margin: 0 0 8px 0;
    color: #374151;
  }
}

.code-block {
  background: #1f2937;
  color: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
}

.time-info {
  background: #f3f4f6;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  
  p {
    margin: 4px 0;
    font-family: monospace;
  }
}

.monitor-tips, .time-tips {
  margin-top: 16px;
  
  h4 {
    margin: 0 0 8px 0;
    color: #374151;
  }
  
  ul {
    margin: 0;
    padding-left: 20px;
  }
  
  code {
    background: #e5e7eb;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 12px;
  }
}

.diagnosis {
  .issue {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 12px;
    
    h4 {
      margin: 0 0 8px 0;
    }
    
    p, ol {
      margin: 8px 0;
    }
    
    &.error {
      background: #fef2f2;
      border-left: 4px solid #dc2626;
    }
    
    &.warning {
      background: #fffbeb;
      border-left: 4px solid #f59e0b;
    }
    
    &.success {
      background: #ecfdf5;
      border-left: 4px solid #059669;
    }
  }
}

@media (max-width: 768px) {
  .debug-content {
    padding: 20px 16px;
  }
  
  .debug-section {
    padding: 16px;
  }
  
  .action-buttons {
    .el-button {
      width: 100%;
      margin-bottom: 8px;
    }
  }
}
</style>
