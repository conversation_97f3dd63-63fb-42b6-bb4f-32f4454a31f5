<template>
  <div class="page-wrapper">
    <headers />
    <div class="content">
      <div class="card-grid">
        <div
          class="line-card"
          v-for="(item, index) in tableData"
          :key="item.lineId"
          @click="toInfo(item.lineId)"
        >
          <div class="card-image">
            <img :src="item.images.split(',')[0]" alt="Line Image" />
          </div>
          <div class="card-body">
            <h3 class="card-title">{{ item.name }}</h3>
            <p class="card-description">{{ item.introduce }}</p>
          </div>
        </div>
      </div>

      <div class="pagination-wrapper">
        <el-pagination
          background
          :page-size="search.pageSize"
          layout="prev, pager, next"
          @current-change="handleCurrentChange"
          :total="total"
        />
      </div>
    </div>
    <bottoms />
  </div>
</template>


<script>
  import {getSysFavorPage} from '../../api/api'
  import headers from '@/components/header'
  import bottoms from '@/components/bottom'
  export default {
    data() {
      return{
        search: {
          userId: "",
          pageSize: 12,
          pageNumber: 1,
        },
        total:100,
        tableData: [],
      }
    },
    components: {
      headers,
      bottoms
    },
    methods: {
      getSysFavorPage() {
        getSysFavorPage(this.search).then(res => {
          if (res.code == 1000) {
            this.tableData = res.data.records
            this.total = res.data.total
          }
        })
      },
      toInfo(id) {
        this.$router.push("/lineInfo?id=" + id)
      },
      handleCurrentChange(val) {
        this.search.pageNumber = val
        this.getSysFavorPage()
      }, 
    },
    created() {
     
    },
    mounted() {
      this.search.userId = JSON.parse(window.localStorage.getItem("user_info")).id
      this.getSysFavorPage()
    }
 }
</script>

<style scoped>
   .page-wrapper {
  padding-bottom: 0px;
  background-color: #f8f9fa;
  
}

.content {
  max-width: 1200px;
  margin: auto;
}

.card-grid {
  margin-top: 50px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.line-card {
  background-color: #fff;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.line-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.card-image {
  height: 160px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.card-body {
  padding: 16px;
}

.card-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px;
  color: #333;
}

.card-description {
  font-size: 0.9rem;
  color: #666;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding-bottom: 20px;
}

</style>