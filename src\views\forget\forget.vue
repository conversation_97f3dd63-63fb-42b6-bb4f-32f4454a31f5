<template>
  <div class="forget-password-page-wrapper"> 
    <div class="forget-password-background"> 
    </div>
    <div class="forget-password-form-container">
      <el-card class="forget-card" shadow="lg">
        <div class="forget-header">
          <img src="../../assets/image/image 2.png" alt="Logo" class="forget-logo" />
          <h2 class="forget-title">重置密码</h2> 
        </div>

        <el-form :model="forgetForm" :rules="forgetRules" ref="forgetFormRef" @submit.native.prevent="handleForgetPassword">
          <el-form-item prop="loginAccount">
            <el-input
              v-model="forgetForm.loginAccount"
              placeholder="请输入您的登录账号"
              prefix-icon="el-icon-user"
              clearable
            />
          </el-form-item>
          <el-form-item prop="email">
            <el-input
              v-model="forgetForm.email"
              placeholder="请输入注册时使用的邮箱"
              prefix-icon="el-icon-message"
              clearable
            />
          </el-form-item>
          <el-form-item prop="code">
            <el-input
              v-model="forgetForm.code"
              placeholder="请输入邮箱验证码"
              prefix-icon="el-icon-s-ticket"
              class="input-with-button"
            >
              <el-button 
                slot="append" 
                @click="handleGetEmailCode" 
                :disabled="isSendingCode"
                :loading="isLoadingCode"
                class="get-code-button"
              >
                {{ codeButtonText }}
              </el-button>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              type="password"
              v-model="forgetForm.password"
              placeholder="请输入新密码 (至少6位)"
              prefix-icon="el-icon-lock"
              show-password
              autocomplete="new-password"
            />
          </el-form-item>
           <el-form-item prop="confirmPassword"> 
            <el-input
              type="password"
              v-model="forgetForm.confirmPassword"
              placeholder="请再次输入新密码"
              prefix-icon="el-icon-check"
              show-password
              autocomplete="new-password"
            />
          </el-form-item>


          <el-form-item>
            <el-button
              type="primary"
              class="submit-button"
              @click="handleForgetPassword"
              :loading="isLoading"
            >
              确认重置密码
            </el-button>
          </el-form-item>
          <el-form-item class="extra-link-item">
            <el-button type="text" @click="navigateToLogin" class="back-to-login-link">
              返回登录
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import { getEmailReg, forgetPassword } from '../../api/api'; // 确认API路径

export default {
  name: 'ForgetPasswordPage', // 1. 组件命名
  data() {
    const validateNewPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入新密码'));
      } else if (value.length < 6) {
        callback(new Error('密码长度不能少于6位'));
      } else {
        if (this.forgetForm.confirmPassword !== '') {
          this.$refs.forgetFormRef.validateField('confirmPassword');
        }
        callback();
      }
    };
    const validateConfirmPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请再次输入新密码'));
      } else if (value !== this.forgetForm.password) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    };
    const validateEmailFormat = (rule, value, callback) => {
        if (!value) {
            return callback(new Error('请输入邮箱'));
        }
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(value)) {
            return callback(new Error('请输入正确的邮箱格式'));
        }
        callback();
    };

    return {
      forgetForm: { // 2. 表单数据对象
        loginAccount: '',
        password: '',
        confirmPassword: '', // 增加确认密码字段
        email: '',
        code: '',
      },
      forgetRules: { // 3. 表单校验规则
        loginAccount: [{ required: true, message: '请输入登录账号', trigger: 'blur' }],
        password: [{ required: true, validator: validateNewPassword, trigger: 'blur' }],
        confirmPassword: [{ required: true, validator: validateConfirmPassword, trigger: 'blur' }],
        email: [
            { required: true, message: '请输入邮箱地址', trigger: 'blur' },
            { validator: validateEmailFormat, trigger: 'blur' }
        ],
        code: [{ required: true, message: '请输入邮箱验证码', trigger: 'blur' }],
      },
      isLoading: false,      // 提交按钮加载状态
      isLoadingCode: false,  // 获取验证码按钮加载状态
      isSendingCode: false,  // 是否正在发送验证码（用于倒计时）
      countdown: 0,          // 倒计时秒数
      codeButtonText: '获取验证码',
    };
  },
  methods: {
    navigateToLogin() {
      this.$router.push('/login');
    },
    async handleGetEmailCode() { // 4. 获取验证码逻辑优化
      // 先校验邮箱字段
      this.$refs.forgetFormRef.validateField('email', async (errorMessage) => {
        if (!errorMessage) { // 邮箱格式正确
          this.isLoadingCode = true;
          this.isSendingCode = true;
          this.codeButtonText = '发送中...';
          try {
            const res = await getEmailReg({ email: this.forgetForm.email });
            if (res && (res.code === 1000 || res.code === 100 || res.code === 200) ) { // 假设100也是成功码
              this.$message.success('验证码已发送至您的邮箱，请注意查收');
              this.startCountdown();
            } else {
              this.$message.error(res.message || '验证码发送失败');
              this.resetCodeButton();
            }
          } catch (error) {
            console.error('获取验证码API异常:', error);
            this.$message.error('验证码发送请求失败，请稍后重试');
            this.resetCodeButton();
          } finally {
            this.isLoadingCode = false;
          }
        } else {
            this.$message.warning('请先输入正确的邮箱地址');
        }
      });
    },
    startCountdown() {
      this.countdown = 60; // 60秒倒计时
      this.codeButtonText = `${this.countdown}s后重发`;
      const timer = setInterval(() => {
        this.countdown--;
        if (this.countdown > 0) {
          this.codeButtonText = `${this.countdown}s后重发`;
        } else {
          clearInterval(timer);
          this.resetCodeButton();
        }
      }, 1000);
    },
    resetCodeButton() {
        this.isSendingCode = false;
        this.codeButtonText = '获取验证码';
        this.countdown = 0;
    },
    handleForgetPassword() { // 5. 重置密码方法优化
      this.$refs.forgetFormRef.validate(async (valid) => {
        if (valid) {
          this.isLoading = true;
          try {
            const params = {
              loginAccount: this.forgetForm.loginAccount,
              password: this.forgetForm.password, // 后端通常需要的是新密码
              email: this.forgetForm.email,
              code: this.forgetForm.code,
            };
            const res = await forgetPassword(params);
            if (res && (res.code === 1000 || res.code === 200)) {
              this.$message.success('密码重置成功！请使用新密码登录。');
              setTimeout(() => {
                this.$router.push('/login');
              }, 1000);
            } else {
              this.$message.error(res.message || '密码重置失败，请检查信息或稍后重试');
            }
          } catch (error) {
            console.error('重置密码API请求异常:', error);
             if (error.response && error.response.data && error.response.data.message) {
                 this.$message.error(`密码重置失败: ${error.response.data.message}`);
            } else {
                this.$message.error('密码重置请求失败，请检查网络或联系管理员');
            }
          } finally {
            this.isLoading = false;
          }
        } else {
          console.log('重置密码表单校验失败');
          this.$message.warning('请检查表单信息是否填写正确。');
          return false;
        }
      });
    },
  },
  beforeDestroy() { // 清除倒计时定时器
      if (this.codeButtonText.includes('s后重发')) { // 简单判断是否在倒计时
          const timers = setInterval(() => {}, 1000); // 获取一个定时器ID的技巧
          for (let i = 0; i <= timers; i++) { // 清除所有可能的定时器
              clearInterval(i);
          }
      }
  }
};
</script>

<style scoped lang="scss">
// --- SCSS Variables (与 login.vue 保持一致或全局定义) ---
$primary-color: #409EFF;
$brand-color: #d37136; 
$text-color-primary: #303133;
$text-color-on-dark-bg: #ffffff;
$login-card-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
$input-height: 40px;
$border-color-light: #dcdfe6;
$text-color-secondary: #909399;
$success-color: #67C23A;
// --- Page Wrapper (与 login.vue 样式类似) ---
.forget-password-page-wrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: linear-gradient(120deg, #fdfbfb 0%, #ebedee 100%); // 示例浅色渐变背景
}

// --- Forget Password Form ---
.forget-password-form-container {
  //
}

.forget-card {
  width: 400px; // 表单卡片宽度
  padding: 20px 25px 25px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.95); // 半透明背景，比登录页略实一点
  backdrop-filter: blur(3px); // 轻微毛玻璃
  -webkit-backdrop-filter: blur(3px);
  box-shadow: $login-card-shadow;

  .forget-header {
    text-align: center;
    margin-bottom: 20px;

    .forget-logo {
      width: 40px;
      height: auto;
      vertical-align: middle;
      margin-right: 8px;
    }

    .forget-title {
      font-size: 20px; // 标题字号
      color: $text-color-primary; 
      font-weight: 500;
      display: inline-block;
      vertical-align: middle;
    }
  }

  .el-form-item {
    margin-bottom: 18px;

    ::v-deep .el-input__inner {
      height: $input-height;
      line-height: $input-height;
      border-radius: 4px;
      &:focus {
        border-color: $primary-color;
      }
    }
    ::v-deep .el-input__prefix {
        display: flex;
        align-items: center;
        height: 100%;
     }
  }
  
  .input-with-button { // 验证码输入框与按钮组合
          .get-code-button { // 使用您在模板中为按钮添加的类名
        // 设置为您想要的颜色
        background-color: $success-color; // 例如，使用绿色背景 (需先定义 $success-color)
        border-color: $success-color;     // 边框也用绿色
        color: #fff;                     // 文字用白色

        &:hover {
          background-color: lighten($success-color, 10%); // 悬浮时颜色变浅
          border-color: lighten($success-color, 10%);
          color: #fff;
        }

        // 禁用和加载状态的颜色也可以按需调整
        &.is-disabled, &:disabled {
          background-color: #f7f7f7; // 保持 Element UI 默认的禁用背景色
          border-color: $border-color-light;
          color: $text-color-secondary; // 保持 Element UI 默认的禁用文字颜色
        }
        &.is-loading {
          // 如果需要，可以调整加载状态的样式
        }
      }
    ::v-deep .el-input-group__append {
      
      background-color: transparent; // 使按钮背景透明，以便自定义
      padding: 0;
      border: none;
      button {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
        height: $input-height; // 与输入框等高
        font-size: 13px;
        padding: 0 15px; // 按钮内边距调整
        &:disabled {
            color: #c0c4cc;
            cursor: not-allowed;
            background-image: none;
            background-color: #f5f7fa; // 禁用时背景色
            border-color: #e4e7ed;
        }
      }
    }
  }

  .submit-button { // 提交按钮
    width: 100%;
    height: $input-height;
    font-size: 15px;
    border-radius: 4px;
    letter-spacing: 1px;
  }
  .extra-link-item { // “返回登录”
      margin-top: 15px;
      margin-bottom: 0;
      text-align: center;
      .back-to-login-link {
          font-size: 13px;
          color: $text-color-secondary;
           &:hover {
            color: $primary-color;
           }
      }
  }
}
// 确保 login.css 中的样式不会与此处的 scoped 样式产生不期望的冲突
// 如果 login.css 定义了全局背景，您可能需要在 .forget-password-page-wrapper 上覆盖它。
</style>