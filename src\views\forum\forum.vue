<template>
  <div class="forum-page">
    <headers />

    <div class="forum-main-content">
      <el-card class="search-panel-forum" shadow="never">
        <el-form :inline="true" :model="searchParams" size="small" class="search-form-forum" @submit.native.prevent="handleSearch">
          <el-form-item label="资讯标题">
            <el-input
              v-model="searchParams.title"
              placeholder="请输入资讯标题进行搜索"
              clearable
              class="search-input-forum"
              @keyup.enter.native="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearchForm" icon="el-icon-refresh" v-if="searchParams.title">重置</el-button> 
          </el-form-item>
        </el-form>
      </el-card>

      <div v-loading="isLoading" class="list-container-forum">
        <el-card shadow="never" class="forum-list-card" v-if="forumList.length > 0">
          <div 
            v-for="(item, index) in forumList" 
            :key="item.id || index" 
            class="forum-list-item"
            @click="navigateToDetail(item.id)"
          >
            <span class="item-index">{{ (searchParams.pageNumber - 1) * searchParams.pageSize + index + 1 }}.</span>
            <span class="item-title" :title="item.title">{{ item.title }}</span>
            <span class="item-date" v-if="item.createTime">{{ item.createTime | formatDate }}</span> 
            <i class="el-icon-arrow-right item-arrow"></i>
          </div>
        </el-card>
        <el-empty v-else description="暂无相关资讯" class="forum-empty"></el-empty>
      </div>

      <el-pagination
        v-if="totalItems > 0 && forumList.length > 0"
        class="forum-pagination"
        background
        :current-page.sync="searchParams.pageNumber"
        :page-size="searchParams.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="totalItems"
        @current-change="handlePageChange"
      />
    </div>
    <bottoms />
  </div>
</template>

<script>
import { getSysForumPage } from '../../api/api'; // 确认API路径和函数名
import headers from '@/components/header';
import bottoms from '@/components/bottom';

// 简单的日期格式化过滤器 (如果需要)
const formatDateFilter = (value) => {
  if (!value) return '';
  const date = new Date(value);
  // 返回 YYYY-MM-DD 格式，您可以根据需要调整
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

export default {
  name: 'ForumListPage', // 1. 组件命名
  components: {
    headers,
    bottoms,
  },
  filters: { // 4. 日期格式化过滤器 (如果需要)
    formatDate(value) {
      return formatDateFilter(value);
    }
  },
  data() {
    return {
      searchParams: { // 2. 变量名优化
        title: '',
        pageSize: 15, // 每页显示条数调整
        pageNumber: 1,
      },
      totalItems: 0,   // 2. 变量名优化
      forumList: [],   // 2. 变量名优化
      isLoading: false, // 3. 加载状态
    };
  },
  methods: {
    // 5. API调用封装和错误处理
    async fetchForumData() {
      this.isLoading = true;
      try {
        const params = { ...this.searchParams };
        const res = await getSysForumPage(params);
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.forumList = res.data.records || [];
          this.totalItems = Number(res.data.total) || 0;
        } else {
          this.$message.error(res.message || '获取资讯列表失败');
          this.forumList = [];
          this.totalItems = 0;
        }
      } catch (error) {
        console.error('获取资讯列表失败:', error);
        this.$message.error('网络繁忙，请稍后重试');
        this.forumList = [];
        this.totalItems = 0;
      } finally {
        this.isLoading = false;
      }
    },
    // 6. 搜索与重置
    handleSearch() {
      this.searchParams.pageNumber = 1;
      this.fetchForumData();
    },
    resetSearchForm() {
      this.searchParams.title = '';
      this.searchParams.pageNumber = 1;
      this.fetchForumData();
    },
    // 7. 导航
    navigateToDetail(id) {
      this.$router.push({ path: '/forumInfo', query: { id } }); // 使用对象形式
    },
    // 8. 分页
    handlePageChange(page) {
      this.searchParams.pageNumber = page;
      this.fetchForumData();
    },
  },
  created() {
    this.fetchForumData(); // 9. 组件创建时加载数据
  },
};
</script>

<style scoped lang="scss">
// --- SCSS Variables ---
$primary-color: #409EFF;
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$bg-color-page: #f5f7fa;
$card-shadow: 0 1px 4px rgba(0,0,0,.04); // 更轻的阴影
$card-border-radius: 6px;
$font-family-base: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;

// --- Page Container ---
.forum-page {
  font-family: $font-family-base;
  background-color: $bg-color-page;
  min-height: calc(100vh - 120px); 
  display: flex;
  flex-direction: column;
}

.forum-main-content {
  width: 75%; // 内容区宽度调整
  max-width: 1000px; // 最大宽度
  margin: 20px auto;
  flex-grow: 1;
}

// --- Search Panel ---
.search-panel-forum {
  margin-bottom: 20px;
  border-radius: $card-border-radius;
  padding: 10px 20px; // 内边距调整

  .search-form-forum {
    .el-form-item {
      margin-bottom: 0; 
      margin-right: 15px;
      &:last-child { margin-right: 0; }
    }
    .search-input-forum {
      width: 280px; 
    }
  }
}

// --- List Container ---
.list-container-forum {
  min-height: 300px; // 至少给列表一个高度，避免加载时页面跳动
  background-color: #fff;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  border: 1px solid $border-color-lighter;
}

.forum-list-card { // 如果列表项本身用卡片，可以去掉外层卡片
  border: none; // 移除 el-card 默认边框，因为外层 list-container-forum 已有
  box-shadow: none;
  ::v-deep .el-card__body {
    padding: 0; // 列表项的padding由 item 自身控制
  }
}

.forum-list-item {
  display: flex;
  align-items: center;
  padding: 12px 20px; // 列表项内边距
  border-bottom: 1px solid $border-color-lighter;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 14px;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f9fafc; // 悬浮背景色
    .item-title {
      color: $primary-color; // 标题颜色变化
    }
  }

  .item-index {
    color: $text-color-secondary;
    margin-right: 8px;
    font-weight: 500;
    min-width: 25px; // 给序号一个最小宽度，使其对齐
  }

  .item-title {
    color: $text-color-primary;
    flex-grow: 1; // 标题占据剩余空间
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis; // 单行溢出显示省略号
    transition: color 0.2s ease;
  }

  .item-date {
    color: $text-color-secondary;
    font-size: 13px;
    margin-left: 15px; // 日期与标题间距
    flex-shrink: 0; // 防止日期被压缩
  }
  
  .item-arrow {
    color: $text-color-secondary;
    margin-left: 10px;
    font-size: 12px;
  }
}

// --- Empty State ---
.forum-empty {
  width: 100%;
  padding: 40px 0;
  min-height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: transparent; // 如果外层list-container-forum有背景和边框
}

// --- Pagination ---
.forum-pagination {
  margin-top: 25px;
  padding-bottom: 20px;
  text-align: center;
}
</style>