<template>
  <div class="forum-detail-page"> 
    <headers />

    <div class="forum-detail-main-content">
      <el-card v-if="!isLoading.page && forumDetail.id" class="content-card-forum" shadow="never"> 
        <div slot="header" class="clearfix card-header-forum">
          <h1 class="forum-title">{{ forumDetail.title }}</h1>
          <div class="meta-info" v-if="forumDetail.createTime || forumDetail.author"> 
            <span v-if="forumDetail.author" class="author"><i class="el-icon-user"></i> {{ forumDetail.author }}</span>
            <span v-if="forumDetail.createTime" class="publish-date"><i class="el-icon-time"></i> {{ forumDetail.createTime | formatDate }}</span>
            </div>
        </div>
        
        <div class="forum-content-html" v-html="processedContent"></div>
      </el-card>
      
      <div v-if="isLoading.page" class="page-loading">
        <i class="el-icon-loading"></i> <span>内容加载中...</span>
      </div>
      
      <el-empty 
        v-if="!isLoading.page && !forumDetail.id" 
        description="资讯详情加载失败或不存在" 
        class="page-empty-forum"
      >
        <el-button type="primary" size="small" @click="$router.go(-1)">返回上一页</el-button>
      </el-empty>
    </div>

    <bottoms />
  </div>
</template>

<script>
import { getSysForumById } from '../../api/api'; // 确认API路径
import headers from '@/components/header';
import bottoms from '@/components/bottom';

// 简单的日期格式化过滤器
const formatDateFilter = (value) => {
  if (!value) return '';
  const date = new Date(value);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2,'0')}:${String(date.getMinutes()).padStart(2,'0')}`;
};

export default {
  name: 'ForumDetailPage', // 1. 组件命名
  components: { headers, bottoms },
  filters: { // 4. 过滤器
    formatDate(value) {
      return formatDateFilter(value);
    }
  },
  data() {
    return {
      forumId: null, // 从路由获取
      isLoading: { // 3. 加载状态
        page: true,
      },
      forumDetail: { // 2. 初始化数据结构
        id: null,
        title: '',
        content: '', // 富文本内容
        images: '', // 如果有轮播图，则保留，否则可移除
        createTime: '', // 假设有创建时间
        author: '',     // 假设有作者
      },
    };
  },
  computed: {
    // 如果仍有轮播图需求，保留此计算属性
    // imageUrls() {
    //   if (this.forumDetail.images && typeof this.forumDetail.images === 'string') {
    //     return this.forumDetail.images.split(',').map(imgUrl => imgUrl.trim()).filter(url => url);
    //   }
    //   return [];
    // },
    processedContent() {
        // 可以在这里对 this.forumDetail.content 做一些预处理，如果需要
        // 例如，使用 DOMPurify 清理HTML等
        return this.forumDetail.content;
    }
  },
  methods: {
    // 5. API调用封装和错误处理
    async fetchForumDetail() {
      if (!this.forumId) {
        this.isLoading.page = false;
        this.forumDetail.content = '<p>无效的资讯ID。</p>'; // 更新占位内容
        return;
      }
      this.isLoading.page = true;
      try {
        const res = await getSysForumById({ id: this.forumId });
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.forumDetail = res.data;
           // 如果后端返回的 content 是纯文本且包含换行符，可能需要转换为 <p> 标签
           if (this.forumDetail.content && !this.forumDetail.content.includes('<p>')) {
             this.forumDetail.content = this.forumDetail.content.replace(/\n/g, '<br>');
           }
        } else {
          this.$message.error(res.message || '获取资讯详情失败');
          this.forumDetail.title = '加载失败';
          this.forumDetail.content = '<p>获取资讯详情失败，请稍后重试。</p>';
        }
      } catch (error) {
        console.error("获取资讯详情异常:", error);
        this.$message.error('网络错误，资讯详情加载失败');
        this.forumDetail.title = '网络错误';
        this.forumDetail.content = '<p>网络错误，资讯详情加载失败。</p>';
      } finally {
        this.isLoading.page = false;
      }
    },
    async initializePage() {
      this.forumId = this.$route.query.id;
      if (this.forumId) {
        await this.fetchForumDetail();
      } else {
        this.$message.error('未找到指定的资讯ID');
        this.isLoading.page = false;
        this.forumDetail.title = '错误';
        this.forumDetail.content = '<p>未找到指定的资讯。</p>';
        //考虑跳转: this.$router.replace('/forum'); 
      }
    }
  },
  created() {
    this.initializePage();
  },
  watch: {
    // 6. 监听路由ID变化，重新加载数据
    '$route.query.id'(newId) {
      if (newId && newId !== this.forumId) {
        this.initializePage();
      }
    }
  }
};
</script>

<style scoped lang="scss">
// --- SCSS Variables ---
$primary-color: #409EFF;
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5;
$bg-color-page: #f5f7fa;
$card-bg-light: #ffffff;
$card-shadow: 0 2px 8px rgba(0,0,0,.05);
$card-border-radius: 6px;

// --- Page Container ---
.forum-detail-page {
  font-family: 'Helvetica Neue', Helvetica, /* ... */;
  background-color: $bg-color-page;
  min-height: calc(100vh - 120px); 
  display: flex;
  flex-direction: column;
}

.forum-detail-main-content {
  width: 65%; // 内容区宽度调整，详情页可以窄一些
  max-width: 800px; // 最大宽度
  margin: 25px auto; // 上下边距
  flex-grow: 1;
}

.content-card-forum {
  background-color: $card-bg-light;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  border: 1px solid $border-color-lighter; // 更柔和的边框

  // 卡片头部 (标题和元信息)
  .card-header-forum {
    padding-bottom: 15px; // 头部与内容分割线之间的间距
    border-bottom: 1px solid $border-color-lighter;
    margin-bottom: 20px; // 与富文本内容的间距
  }

  .forum-title {
    font-size: 24px; // 标题加大
    font-weight: 600;
    color: $text-color-primary;
    margin: 0 0 10px 0; // 与下方元信息的间距
    line-height: 1.4;
    text-align: center; // 标题居中
  }
  
  .meta-info {
    display: flex;
    justify-content: center; // 元信息居中
    align-items: center;
    font-size: 13px;
    color: $text-color-secondary;
    gap: 15px; // 元信息项之间的间距

    .author, .publish-date {
      display: flex;
      align-items: center;
      i {
        margin-right: 5px;
        font-size: 14px;
      }
    }
  }

  ::v-deep .el-card__body {
    padding: 20px 25px; // 卡片主体内边距
  }
}

// --- Carousel Section (如果保留轮播图) ---
.carousel-section {
  margin: -20px -25px 25px -25px; // 使轮播图宽度占满卡片，并与标题有间距
  border-bottom: 1px solid $border-color-lighter; // 与下方内容分隔
  .line-carousel { /* 或 .forum-carousel */
    // border-radius: $card-border-radius - 2px; 
    // overflow: hidden;
  }
  .carousel-image { width: 100%; height: 100%; display: block; }
  .image-slot { /* ...与之前页面类似... */ 
    display: flex; flex-direction: column; justify-content: center; align-items: center;
    width: 100%; height: 100%; background: #f0f2f5; color: $text-color-secondary;
    font-size: 14px; text-align: center;
    i { font-size: 32px; margin-bottom: 8px; }
  }
}
.image-empty-placeholder, .page-empty-placeholder {
    padding: 40px 0;
    min-height: 200px;
}


// --- HTML Content Area (v-html) ---
.forum-content-html {
  font-size: 15px; 
  line-height: 1.8; 
  color: $text-color-regular;
  word-wrap: break-word; // 确保长单词能换行

  // 使用 ::v-deep 或 /deep/ 来穿透 scoped CSS 限制，为 v-html 内容设置样式
  ::v-deep {
    p {
      margin: 0 0 1.2em 0; // 段落下边距
      &:last-child {
        margin-bottom: 0;
      }
    }
    img {
      max-width: 100% !important;
      height: auto !important;
      display: block; 
      margin: 15px auto; // 图片上下边距并居中
      border-radius: 4px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.08); // 图片阴影
    }
    h1, h2, h3, h4, h5, h6 {
      color: $text-color-primary;
      margin: 1.8em 0 0.8em 0; // 标题上下边距
      font-weight: 600; // 标题字重
      line-height: 1.4;
      &:first-child {
        margin-top: 0;
      }
    }
    h1 { font-size: 1.7em; }
    h2 { font-size: 1.5em; }
    h3 { font-size: 1.3em; }
    
    ul, ol {
      padding-left: 20px; // 列表缩进调整
      margin-bottom: 1.2em;
      li {
        margin-bottom: 0.6em;
        line-height: 1.7;
      }
    }
    a {
      color: $primary-color;
      text-decoration: none;
      border-bottom: 1px solid transparentize($primary-color, 0.7);
      transition: border-color 0.2s ease;
      &:hover {
        border-bottom-color: $primary-color;
      }
    }
    blockquote {
      margin: 1.5em 0;
      padding: 10px 15px;
      border-left: 3px solid $primary-color; // 引用块左边框颜色
      background-color: #f8f9fa; // 引用块背景色
      color: $text-color-regular;
      p { margin-bottom: 0.5em; &:last-child { margin-bottom: 0;}}
    }
    table { /* ...与之前页面类似... */ 
        width: auto; border-collapse: collapse; margin: 1.5em 0; font-size: 0.9em;
        th, td { border: 1px solid $border-color-light; padding: 8px 10px; text-align: left; }
        th { background-color: #f9fafc; font-weight: 500; }
    }
    pre, code {
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
        background-color: #f6f8fa;
        border-radius: 3px;
        padding: 0.2em 0.4em;
        font-size: 0.9em;
    }
    pre {
        padding: 1em;
        overflow-x: auto;
    }
  }
}

// --- Page Loading State ---
.page-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px; // 确保加载状态有足够高度
  color: $text-color-secondary;
  font-size: 14px;
  i {
    font-size: 28px;
    margin-bottom: 10px;
    color: $primary-color;
    animation: rotating 1.5s linear infinite;
  }
}
@keyframes rotating { 
  from { transform: rotate(0deg); } 
  to { transform: rotate(360deg); } 
}

</style>