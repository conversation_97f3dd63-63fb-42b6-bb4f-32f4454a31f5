<template>
  <div class="hotel-page"> 
    <headers />

    <div class="hotel-main-content">
      <el-card class="search-panel-hotel" shadow="never">
        <el-form :inline="true" :model="searchParams" size="small" class="search-form-hotel">
          <el-form-item label="所属景点">
            <el-select 
              v-model="searchParams.attractions" 
              placeholder="请选择景点" 
              clearable 
              filterable
              class="search-select"
            >
              <el-option 
                v-for="attraction in attractionsListForFilter" 
                :key="attraction.id || attraction.name" 
                :label="attraction.name"
                :value="attraction.name" 
              />
              //{/* 如果景点很多，可以考虑后端也支持按景点名称模糊搜索加载下拉列表 */}
            </el-select>
          </el-form-item>
          <el-form-item label="酒店名称">
            <el-input 
              v-model="searchParams.name" 
              placeholder="请输入酒店名称" 
              clearable 
              class="search-input"
              @keyup.enter.native="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearchForm" icon="el-icon-refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <div v-loading="isLoading" class="list-container-hotel">
        <el-row :gutter="20" class="hotel-list-row" v-if="hotelsList.length > 0">
          <el-col
            :xs="24" :sm="12" :md="8" :lg="6"
            v-for="hotel in hotelsList"
            :key="hotel.id"
            class="hotel-col"
          >
            <el-card 
              shadow="hover" 
              class="hotel-card" 
              :body-style="{ padding: '0px' }"
            >
              <el-image
                class="hotel-image"
                :src="hotel.images && hotel.images.split(',')[0]"
                fit="cover"
                lazy
              >
                <div slot="placeholder" class="image-slot loading">
                  <i class="el-icon-loading"></i>
                </div>
                <div slot="error" class="image-slot error">
                  <i class="el-icon-picture-outline"></i> <span>图片加载失败</span>
                </div>
              </el-image>
              <div class="hotel-info">
                <h3 class="hotel-name" :title="hotel.name">{{ hotel.name | truncate(20) }}</h3>
                <div class="hotel-details">
                  <span v-if="hotel.attractionsName" class="hotel-attraction-tag">
                    <i class="el-icon-location-outline"></i> {{ hotel.attractionsName }}
                  </span>
                  </div>
                <p class="hotel-introduce" :title="hotel.introduce">
                  {{ hotel.introduce | truncate(45) }}
                </p>
                <el-button
                  type="warning" 
                  plain
                  size="small"
                  class="booking-button"
                  @click="navigateToHotelDetail(hotel.id)"
                >
                  查看详情并预订
                </el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-empty v-else description="暂无相关酒店信息" class="hotel-empty"></el-empty>
      </div>

      <el-pagination
        v-if="totalItems > 0 && hotelsList.length > 0"
        class="hotel-pagination"
        background
        :current-page.sync="searchParams.pageNumber"
        :page-size="searchParams.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="totalItems"
        @current-change="handlePageChange"
      />
    </div>
    <bottoms />
  </div>
</template>

<script>
import { getSysHotelPage, getSysAttractionsList } from '../../api/api'; // 确认API路径和函数名
import headers from '@/components/header';
import bottoms from '@/components/bottom';

export default {
  name: 'HotelListPage',
  components: {
    headers,
    bottoms,
  },
  data() {
    return {
      searchParams: {
        name: '',
        state: '1', // 假设"1"为有效状态
        attractions: '', // 对应 el-select 的 v-model
        pageSize: 12,
        pageNumber: 1,
      },
      totalItems: 0,
      hotelsList: [],
      attractionsListForFilter: [], // 用于景点筛选下拉列表
      isLoading: false,
    };
  },
  filters: {
    truncate(text, length, suffix = '...') {
      if (typeof text !== 'string') return '';
      if (text.length > length) {
        return text.substring(0, length) + suffix;
      }
      return text;
    },
  },
  methods: {
    async fetchInitialData() {
      this.isLoading = true;
      try {
        // 并行获取景点列表和酒店列表的初始数据
        await Promise.all([
          this.fetchAttractionsForFilter(),
          this.fetchHotelsData(), // 首次加载酒店数据
        ]);
      } catch (error) {
        // 统一的错误处理已在各自方法中，这里可选择是否再次提示
        console.error("页面初始化数据加载失败:", error);
      } finally {
        this.isLoading = false; // 确保在所有请求完成后关闭 loading
      }
    },
    async fetchAttractionsForFilter() {
      try {
        const res = await getSysAttractionsList(); // 假设这个接口返回所有景点列表
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.attractionsListForFilter = res.data;
        } else {
          this.$message.error(res.message || '获取景点筛选列表失败');
        }
      } catch (error) {
        console.error('获取景点筛选列表异常:', error);
        // 此处错误不影响主要酒店列表加载，可选择静默处理或轻提示
      }
    },
    async fetchHotelsData() {
      this.isLoading = true; // 单独的加载状态，或由 fetchInitialData 控制
      try {
        const params = { ...this.searchParams };
        // 如果后端期望 attractions 字段为景点ID而不是名称，需要在这里做转换
        // params.attractionId = this.getAttractionIdByName(this.searchParams.attractions);

        const res = await getSysHotelPage(params);
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.hotelsList = res.data.records || [];
          this.totalItems = Number(res.data.total) || 0;
        } else {
          this.$message.error(res.message || '获取酒店数据失败');
          this.hotelsList = [];
          this.totalItems = 0;
        }
      } catch (error) {
        console.error('获取酒店列表失败:', error);
        this.$message.error('网络繁忙，请稍后重试');
        this.hotelsList = [];
        this.totalItems = 0;
      } finally {
        this.isLoading = false;
      }
    },
    handleSearch() {
      this.searchParams.pageNumber = 1;
      this.fetchHotelsData();
    },
    resetSearchForm() {
      this.searchParams = {
        ...this.searchParams, // 保留pageSize等其他可能不变的参数
        name: '',
        attractions: '',
        pageNumber: 1,
      };
      this.fetchHotelsData();
    },
    navigateToHotelDetail(id) {
      this.$router.push({ path: '/hotelInfo', query: { id } });
    },
    handlePageChange(page) {
      this.searchParams.pageNumber = page;
      this.fetchHotelsData();
    },
  },
  created() {
    this.fetchInitialData(); // 组件创建时加载所有初始数据
  },
};
</script>

<style scoped lang="scss">
// --- SCSS Variables (与 attractions.vue 和 line.vue 保持一致或全局定义) ---
$primary-color: #409EFF;
$warning-color: #E6A23C; // 预订按钮用橙色系
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$border-color-light: #e4e7ed;
$bg-color-page: #f5f7fa;
$card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
$card-hover-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
$card-border-radius: 8px;
$font-family-base: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;

// --- Page Container ---
.hotel-page {
  font-family: $font-family-base;
  background-color: $bg-color-page;
  min-height: calc(100vh - 120px); // 假设 header 和 bottom 各占 60px
  display: flex;
  flex-direction: column;
}

.hotel-main-content {
  width: 85%;
  max-width: 1300px;
  margin: 20px auto;
  flex-grow: 1;
}

// --- Search Panel ---
.search-panel-hotel {
  margin-bottom: 25px;
  border-radius: $card-border-radius;
  padding: 5px 15px; 

  .search-form-hotel {
    .el-form-item {
      margin-bottom: 0; 
      margin-right: 10px; // 表单项间距减小
      &:last-child { margin-right: 0; }
    }
    .search-select,
    .search-input {
      width: 200px; // 统一输入框/选择框宽度
    }
  }
}

// --- List Container ---
.list-container-hotel {
  min-height: 300px;
}

// --- Hotel Card ---
.hotel-col {
  margin-bottom: 20px;
}

.hotel-card {
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  transition: transform 0.25s ease-in-out, box-shadow 0.25s ease-in-out;
  height: 100%;
  display: flex;
  flex-direction: column;
  // 卡片整体可点击效果由 @click.native 实现，如果需要手型光标：
  // cursor: pointer; 

  &:hover {
    transform: translateY(-4px);
    box-shadow: $card-hover-shadow;
  }

  .hotel-image { // el-image
    width: 100%;
    height: 180px;
    display: block;
    background-color: #eee;
  }

  .image-slot { /* 与 attractions.vue 中样式类似 */
    display: flex; flex-direction: column; justify-content: center; align-items: center;
    width: 100%; height: 100%; background: #f5f7fa; color: $text-color-secondary;
    font-size: 13px; text-align: center;
    &.loading i { font-size: 24px; color: $primary-color; animation: rotating 2s linear infinite; }
    &.error i { font-size: 28px; margin-bottom: 8px; }
  }
  @keyframes rotating { /* ... */ }

  .hotel-info {
    padding: 12px 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    text-align: left;
  }

  .hotel-name {
    font-size: 16px;
    color: $text-color-primary;
    font-weight: 500;
    margin: 0 0 6px 0;
    line-height: 1.4;
    overflow: hidden; text-overflow: ellipsis; display: -webkit-box;
    -webkit-line-clamp: 1; /* 酒店名称通常一行显示 */
    -webkit-box-orient: vertical;
    min-height: 1.4em; // 预留一行高度
  }
  
  .hotel-details {
    font-size: 12px;
    color: $text-color-secondary;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    flex-wrap: wrap; // 如果有多个标签，允许换行

    .hotel-attraction-tag {
      background-color: lighten($primary-color, 35%);
      color: darken($primary-color, 10%);
      padding: 2px 6px;
      border-radius: 3px;
      margin-right: 8px;
      margin-bottom: 4px; // 换行时的间距
      i { margin-right: 3px; }
    }
    // .el-rate { height: auto; line-height: 1; } // 如果使用 el-rate
  }

  .hotel-introduce {
    font-size: 13px;
    color: $text-color-regular;
    line-height: 1.5;
    margin-bottom: 12px;
    flex-grow: 1;
    overflow: hidden; text-overflow: ellipsis; display: -webkit-box;
    -webkit-line-clamp: 2; /* 酒店介绍截断为2行 */
    -webkit-box-orient: vertical;
    min-height: calc(1.5em * 2); 
  }

  .booking-button { // el-button
    width: 100%;
    margin-top: auto;
  }
}

// --- Empty State ---
.hotel-empty { /* ...与 line.vue 类似... */ 
  width: 100%; padding: 30px 0; min-height: 300px; display: flex;
  justify-content: center; align-items: center;
}

// --- Pagination ---
.hotel-pagination { /* ...与 line.vue 类似... */ 
  margin-top: 30px; padding-bottom: 20px; text-align: center;
}
</style>