<template>
  <div class="hotel-detail-page">
    <headers />

    <div v-loading="isLoading.page" class="hotel-detail-main-content">
      <el-row :gutter="30" v-if="hotelDetail.id" class="info-panel">
        <el-col :xs="24" :md="14" class="carousel-col">
          <el-carousel height="450px" class="hotel-carousel" v-if="imageUrls.length > 0">
            <el-carousel-item v-for="(imgUrl, index) in imageUrls" :key="index">
              <el-image class="carousel-image" :src="imgUrl" fit="cover" >
                <div slot="placeholder" class="image-slot loading"><i class="el-icon-loading"></i></div>
                <div slot="error" class="image-slot error"><i class="el-icon-picture-outline"></i></div>
              </el-image>
            </el-carousel-item>
          </el-carousel>
          <el-empty v-else description="暂无酒店图片" class="image-empty"></el-empty>
        </el-col>

        <el-col :xs="24" :md="10" class="info-col">
          <h2 class="hotel-title">{{ hotelDetail.name }}</h2>
          <el-descriptions :column="1" border size="small" class="hotel-descriptions">
            <el-descriptions-item v-if="hotelDetail.attractions" label-class-name="info-label" label="所属景区">{{ hotelDetail.attractions }}</el-descriptions-item>
            <el-descriptions-item label-class-name="info-label" label="地址">{{ hotelDetail.address }}</el-descriptions-item>
            </el-descriptions>
          <div class="hotel-introduce-wrapper">
            <h4>酒店介绍</h4>
            <p class="hotel-introduce-text">{{ hotelDetail.introduce || '暂无详细介绍。' }}</p>
          </div>
          <el-button 
            type="warning" 
            icon="el-icon-shopping-cart-2" 
            @click="openBookingDialog" 
            class="book-now-button"
            :disabled="roomTypes.length === 0 && !isLoading.rooms"
          >
            {{ roomTypes.length > 0 ? '立即预订' : (isLoading.rooms ? '房型加载中...' : '暂无可预订房型') }}
          </el-button>
        </el-col>
      </el-row>
      <el-empty v-else-if="!isLoading.page" description="酒店信息加载失败或不存在" class="page-empty"></el-empty>
    </div>

    <el-dialog
      :title="bookingDialog.title"
      :visible.sync="bookingDialog.visible"
      width="600px" 
      :before-close="handleBookingDialogClose"
      class="booking-dialog"
      append-to-body
      destroy-on-close
      @opened="onBookingDialogOpened"
    >
      <el-form 
        v-if="bookingDialog.step === 1" 
        :model="bookingForm" 
        :rules="bookingRules" 
        ref="bookingFormRef" 
        label-width="90px" 
        size="small"
        @submit.native.prevent="submitBooking"
      >
        <el-form-item label="选择房型" prop="selectedRoomTypeId">
          <div class="room-type-list">
            <el-radio-group v-model="bookingForm.selectedRoomTypeId" class="room-radio-group">
              <el-card 
                shadow="hover" 
                v-for="room in roomTypes" 
                :key="room.id" 
                class="room-type-card"
                :class="{ 'is-selected': bookingForm.selectedRoomTypeId === room.id }"
                @click.native="bookingForm.selectedRoomTypeId = room.id" 
              >
                <div class="room-type-name">{{ room.name }}</div>
                <div class="room-type-details">
                  <span>库存: {{ room.num }}</span>
                  <span>价格: <span class="room-price">¥{{ room.price }}</span></span>
                </div>
              </el-card>
            </el-radio-group>
            <el-empty description="暂无可用房型" v-if="!isLoading.rooms && roomTypes.length === 0" image-size="80"></el-empty>
            <div v-if="isLoading.rooms" class="rooms-loading">房型加载中... <i class="el-icon-loading"></i></div>
          </div>
        </el-form-item>
        <el-form-item label="预订数量" prop="quantity">
          <el-input-number v-model="bookingForm.quantity" :min="1" :max="selectedRoomMaxQuantity" />
        </el-form-item>
        <el-form-item label="入住日期" prop="bookingDate">
          <el-date-picker
            v-model="bookingForm.bookingDate"
            type="date"
            placeholder="选择入住日期"
            value-format="yyyy-MM-dd"
            :picker-options="bookingDateOptions"
            style="width: 100%;"
          />
        </el-form-item>
        <h4>联系人信息</h4>
        <el-form-item label="姓名" prop="contactName">
          <el-input v-model="bookingForm.contactName" placeholder="请输入联系人姓名" />
        </el-form-item>
        <el-form-item label="电话" prop="contactTel">
          <el-input v-model="bookingForm.contactTel" placeholder="请输入联系人电话" />
        </el-form-item>
        <el-form-item label="身份证" prop="contactIdCard">
          <el-input v-model="bookingForm.contactIdCard" placeholder="请输入联系人身份证号" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="handleBookingDialogClose">取 消</el-button>
        <el-button 
            type="primary" 
            @click="submitBooking" 
            size="small" 
            :loading="bookingDialog.submitting"
            :disabled="!bookingForm.selectedRoomTypeId"
        >
            确认预订
        </el-button>
      </span>
    </el-dialog>

    <bottoms />
  </div>
</template>

<script>
import { getSysHotelById, getSysHotelItemList, saveSysHotelOrder } from '../../api/api';
import headers from '@/components/header';
import bottoms from '@/components/bottom';

// 手机号和身份证的简单校验
const validatePhoneNumber = (rule, value, callback) => {
  if (!value) return callback(new Error('请输入联系电话'));
  if (!/^1[3-9]\d{9}$/.test(value)) {
    callback(new Error('请输入正确的11位手机号码'));
  } else {
    callback();
  }
};
const validateIdCard = (rule, value, callback) => {
  if (!value) return callback(new Error('请输入身份证号'));
  const reg = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
  if (!reg.test(value)) {
    callback(new Error('请输入有效的18位身份证号码'));
  } else {
    callback();
  }
};
const formatDateFilter = (value, format = 'YYYY-MM-DD') => {
  if (!value) return '';
  const date = new Date(value);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  if (format === 'YYYY-MM-DD HH:mm') {
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}`;
  }
  return `${year}-${month}-${day}`;
};

export default {
  name: 'HotelDetailPage',
  components: { headers, bottoms },
  filters: { /* ... truncate filter ... */ },
  data() {
    return {
      hotelId: null,
      isLoading: {
        page: true,
        rooms: false,
      },
      hotelDetail: { // 初始化酒店详情结构
        id: null,
        name: '',
        images: '', // 初始为空字符串
        attractions: '',
        address: '',
        introduce: '',
      },
      roomTypes: [], // 房型列表
      
      bookingDialog: {
        visible: false,
        title: '酒店预订',
        step: 1, // 可以用于多步骤预订流程
        submitting: false,
      },
      bookingForm: { // 预订表单数据
        selectedRoomTypeId: null,
        quantity: 1,
        bookingDate: '',
        contactName: '',
        contactTel: '',
        contactIdCard: '',
      },
      bookingRules: { // 预订表单校验规则
        selectedRoomTypeId: [{ required: true, message: '请选择房型', trigger: 'change' }],
        quantity: [{ required: true, message: '请输入预订数量', trigger: 'blur' }],
        bookingDate: [{ required: true, message: '请选择入住日期', trigger: 'change' }],
        contactName: [{ required: true, message: '请输入联系人姓名', trigger: 'blur' }],
        contactTel: [{ required: true, validator: validatePhoneNumber, trigger: 'blur' }],
        contactIdCard: [{ required: true, validator: validateIdCard, trigger: 'blur' }],
      },
      bookingDateOptions: { // 预订日期选择器配置
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 不能选择今天之前的日期
        },
      }
    };
  },
  computed: {
    imageUrls() {
      if (this.hotelDetail.images) {
        return this.hotelDetail.images.split(',').map(imgRelativePath => {
          // 假设图片路径需要拼接 HOST，如果已经是完整URL则不需要
          if (imgRelativePath.startsWith('http')) return imgRelativePath;
          return (this.$store.state.HOST || '') + imgRelativePath;
        });
      }
      return [];
    },
    selectedRoomMaxQuantity() {
        if (!this.bookingForm.selectedRoomTypeId || !this.roomTypes.length) return 1;
        const selectedRoom = this.roomTypes.find(room => room.id === this.bookingForm.selectedRoomTypeId);
        return selectedRoom ? (selectedRoom.num > 0 ? selectedRoom.num : 1) : 1; // 最大数量不超过库存，至少为1
    },

    // 计算属性：获取当前用户ID
    userId() {
      const userInfoString = window.localStorage.getItem("user_info");
      if (userInfoString) {
        try {
          const userInfo = JSON.parse(userInfoString);
          return userInfo.id;
        } catch (e) {
          console.error('获取用户ID失败:', e);
          return null;
        }
      }
      return null;
    }
  },
  methods: {
    async fetchHotelDetails() {
      if (!this.hotelId) return;
      this.isLoading.page = true;
      try {
        const res = await getSysHotelById({ id: this.hotelId });
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.hotelDetail = res.data;
        } else {
          this.$message.error(res.message || '获取酒店详情失败');
          this.hotelDetail = { images: '' }; // 确保images存在以避免模板错误
        }
      } catch (error) {
        console.error("获取酒店详情异常:", error);
        this.$message.error('网络错误，酒店详情加载失败');
      } finally {
        this.isLoading.page = false;
      }
    },
    async fetchRoomTypes() {
      if (!this.hotelId) return;
      this.isLoading.rooms = true;
      try {
        const res = await getSysHotelItemList({ id: this.hotelId }); // 假设id是酒店ID
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.roomTypes = res.data;
        } else {
          this.$message.error(res.message || '获取房型列表失败');
          this.roomTypes = [];
        }
      } catch (error) {
        console.error("获取房型列表异常:", error);
        this.roomTypes = [];
      } finally {
        this.isLoading.rooms = false;
      }
    },
  openBookingDialog() {
  console.log('openBookingDialog called');
  if (!this.hotelDetail || !this.hotelDetail.id) {
    this.$message.warning('请等待酒店详情加载完毕后再预订');
    return;
  }
  this.bookingForm = {
    selectedRoomTypeId: this.roomTypes.length > 0 ? this.roomTypes[0].id : null,
    quantity: 1,
    // 直接调用模块级的 formatDateFilter 函数
    bookingDate: formatDateFilter(new Date()), // <--- 修改此处
    contactName: '', 
    contactTel: '',
    contactIdCard: '',
  };
  this.bookingDialog.title = `预订酒店：${this.hotelDetail.name || '未知酒店'}`;
  this.bookingDialog.visible = true; 
  if (this.roomTypes.length === 0 && !this.isLoading.rooms) {
      this.fetchRoomTypes();
  }
},
    onBookingDialogOpened() {
        // 对话框打开后，可以重置表单校验状态
        this.$nextTick(() => {
            if (this.$refs.bookingFormRef) {
                this.$refs.bookingFormRef.clearValidate();
            }
        });
    },
    handleBookingDialogClose() {
      this.bookingDialog.visible = false;
      // 不需要手动重置表单，因为 destroy-on-close 会销毁
    },
    submitBooking() {
      console.log('🚀 开始提交酒店预约，当前表单数据:', this.bookingForm);
      console.log('🔍 用户ID:', this.userId);
      console.log('🏨 酒店ID:', this.hotelId);

      this.$refs.bookingFormRef.validate(async (valid) => {
        console.log('📝 表单验证结果:', valid);

        if (valid) {
          this.bookingDialog.submitting = true;
          console.log('📤 开始发送酒店预约API请求...');

          try {
            // 获取当前用户信息
            const userInfo = this.getCurrentUserInfo();
            console.log('👤 当前用户信息:', userInfo);

            // 生成当前本地时间戳
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const seconds = String(now.getSeconds()).padStart(2, '0');
            const currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            console.log('🕐 生成的本地时间:', currentTime);

            const contactInfo = {
              name: this.bookingForm.contactName,
              tel: this.bookingForm.contactTel,
              idCard: this.bookingForm.contactIdCard,
            };

            const params = {
              hotelId: this.hotelId,
              itemId: this.bookingForm.selectedRoomTypeId,
              num: this.bookingForm.quantity,
              time: this.bookingForm.bookingDate,
              people: JSON.stringify(contactInfo), // 后端需要解析这个JSON字符串
              userId: this.userId,
              // ✅ 添加创建者信息 - 使用正确的字段名
              createBy: userInfo.userName || userInfo.loginAccount || userInfo.user_name || '游客',
              // ✅ 添加创建时间
              createTime: currentTime
            };

            console.log('📋 完整的酒店预约API请求参数:', params);
            const res = await saveSysHotelOrder(params);
            console.log('📥 酒店预约API响应结果:', res);

            if (res && (res.code === 1000 || res.code === 200)) {
              this.$message.success('酒店预订成功，请等待确认！');
              this.handleBookingDialogClose();
              // 预订成功后可以跳转到订单页面或刷新当前页面的某些状态
            } else {
              console.error('❌ 酒店预约API返回错误:', res);
              this.$message.error(res.message || '预订失败');
            }
          } catch (error) {
            console.error("❌ 酒店预订异常:", error);
            this.$message.error('网络错误，预订失败');
          } finally {
            this.bookingDialog.submitting = false;
          }
        } else {
          console.log('❌ 酒店预约表单校验失败!');
          return false;
        }
      });
    },

    // 获取当前用户信息的方法
    getCurrentUserInfo() {
      const userInfoString = window.localStorage.getItem("user_info");
      console.log('🔍 获取用户信息 - localStorage内容:', userInfoString);

      if (userInfoString) {
        try {
          const userInfo = JSON.parse(userInfoString);
          console.log('✅ 解析用户信息成功:', userInfo);
          console.log('📋 用户信息字段详情:');
          console.log('  - userName:', userInfo.userName);           // ✅ 正确的字段名
          console.log('  - loginAccount:', userInfo.loginAccount);   // ✅ 正确的字段名
          console.log('  - user_name:', userInfo.user_name);         // 备用字段
          console.log('  - username:', userInfo.username);           // 备用字段
          console.log('  - id:', userInfo.id);

          // 使用正确的字段名获取用户名
          const userName = userInfo.userName || userInfo.loginAccount || userInfo.user_name || userInfo.username || '游客';
          console.log('🎯 最终选择的用户名:', userName);

          return {
            ...userInfo,
            finalUserName: userName
          };
        } catch (e) {
          console.error('❌ 解析用户信息失败:', e);
          return { userName: '游客', finalUserName: '游客' };
        }
      }

      console.log('⚠️ 未找到用户信息，返回默认值');
      return { userName: '游客', finalUserName: '游客' };
    },
  },
  created() {
    this.hotelId = this.$route.query.id;
    if (this.hotelId) {
      this.fetchHotelDetails();
      this.fetchRoomTypes(); // 预加载房型，或在打开对话框时加载
    } else {
      this.$message.error('无效的酒店ID');
      // this.$router.replace('/hotel'); // 或其他错误处理
    }
  },
};
</script>

<style scoped lang="scss">
// --- SCSS Variables ---
$primary-color: #409EFF;
$warning-color: #E6A23C;
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$border-color-base: #DCDFE6;
$border-color-light: #e4e7ed;
$border-color-lighter: #ebeef5; // <--- `$border-color-lighter` 在这里定义
$bg-color-page: #f5f7fa;
$card-shadow: 0 2px 8px rgba(0,0,0,.06);
$card-border-radius: 6px;

// --- Page Container ---
.hotel-detail-page {
  font-family: 'Helvetica Neue', Helvetica, /* ... */;
  background-color: $bg-color-page;
  min-height: calc(100vh - 120px);
}

.hotel-detail-main-content {
  width: 75%; // 内容区宽度
  max-width: 1200px; // 最大宽度
  margin: 25px auto;
  background-color: #fff;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  padding: 25px 30px; // 内边距

  .page-empty { // 页面级空状态
    padding: 50px 0;
  }
}

.info-panel { // 包裹轮播图和右侧信息的el-row
  //
}

// --- Carousel ---
.carousel-col {
  //
}
.hotel-carousel {
  border-radius: $card-border-radius;
  overflow: hidden; // 确保图片圆角
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  .carousel-image { width: 100%; height: 100%; display: block; }
  .image-slot { /* ...与之前页面类似... */ }
}
.image-empty {
    min-height: 300px; // 给空图片状态一个高度
    display: flex; align-items: center; justify-content: center;
    border: 1px dashed $border-color-light;
    border-radius: $card-border-radius;
}


// --- Info Column ---
.info-col {
  display: flex;
  flex-direction: column;

  .hotel-title {
    font-size: 24px;
    font-weight: 600;
    color: $text-color-primary;
    margin: 0 0 15px 0;
    line-height: 1.3;
  }

  .hotel-descriptions {
    margin-bottom: 20px;
    ::v-deep .info-label { // 自定义描述列表标签的类名
      width: 90px; // 统一标签宽度
      color: $text-color-regular;
      font-weight: normal;
    }
  }
  
  .hotel-introduce-wrapper {
    margin-bottom: 25px;
    h4 {
        font-size: 15px;
        color: $text-color-primary;
        margin: 0 0 8px 0;
        font-weight: 500;
        padding-bottom: 5px;
        border-bottom: 1px solid $border-color-lighter;
    }
    .hotel-introduce-text {
        font-size: 14px;
        color: $text-color-regular;
        line-height: 1.7;
        max-height: 150px; // 限制介绍最大高度
        overflow-y: auto; // 超出则滚动
    }
  }

  .book-now-button {
    width: 100%;
    // margin-top: auto; // 如果希望按钮在底部，确保父容器是 flex column 且有足够空间
  }
}

// --- Booking Dialog ---
.booking-dialog {
  ::v-deep .el-dialog__header {
    border-bottom: 1px solid $border-color-light;
    padding: 15px 20px;
  }
  ::v-deep .el-dialog__body {
    padding: 20px 25px 25px 25px;
  }
  .el-form-item { margin-bottom: 18px; }
  .el-form-item h4 { // 联系人信息标题
    font-size: 14px;
    color: $text-color-primary;
    margin: 15px 0 10px 0;
    font-weight: 500;
  }
}

.room-type-list {
  max-height: 200px; // 房型列表如果过多则滚动
  overflow-y: auto;
  border: 1px solid $border-color-light;
  border-radius: 4px;
  padding: 10px;

  .room-radio-group {
    width: 100%;
  }

  .room-type-card {
    margin-bottom: 10px;
    cursor: pointer;
    border: 1px solid $border-color-light; // 默认边框
    transition: border-color 0.2s, box-shadow 0.2s;

    &:last-child { margin-bottom: 0; }
    
    &.is-selected {
      border-color: $primary-color;
      box-shadow: 0 0 5px rgba($primary-color, 0.3);
    }

    ::v-deep .el-card__body {
      padding: 10px 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .room-type-name {
      font-size: 14px;
      color: $text-color-primary;
      font-weight: 500;
    }
    .room-type-details {
      font-size: 12px;
      color: $text-color-secondary;
      text-align: right;
      span { margin-left: 10px; }
      .room-price {
        color: #FF4949;
        font-weight: bold;
      }
    }
  }
  .rooms-loading {
      text-align:center;
      color: $text-color-secondary;
      padding: 10px;
      i { margin-left: 5px; }
  }
}

</style>