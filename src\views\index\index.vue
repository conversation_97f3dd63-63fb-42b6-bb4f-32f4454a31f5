<template>
  <div class="home-page"> 
    <headers />

    <el-carousel height="500px" class="home-carousel">
      <el-carousel-item v-for="item in rotations" :key="item.id"> 
        <el-image 
          class="carousel-image"
          :src="$store.state.HOST + item.image" 
          fit="cover"
          
        >
            <div slot="placeholder" class="image-slot loading-slot">加载中...</div>
            <div slot="error" class="image-slot error-slot"><i class="el-icon-picture-outline"></i></div>
        </el-image>
      </el-carousel-item>
    </el-carousel>

    <div class="home-content-wrapper">
      <section class="content-section recommended-attractions">
        <h2 class="section-title">推荐景点</h2>
        <div class="item-grid" v-if="recommendedAttractions.length > 0">
          <el-card
            shadow="hover"
            class="item-card attraction-item-card"
            v-for="item in recommendedAttractions"
            :key="item.id"  
            :body-style="{ padding: '0px' }"
          >
            <el-image
              class="item-image"
              :src="item.images && item.images.split(',')[0]"
              fit="cover"
              lazy
            >
                <div slot="placeholder" class="image-slot loading-slot">加载中...</div>
                <div slot="error" class="image-slot error-slot"><i class="el-icon-picture-outline"></i></div>
            </el-image>
            <div class="item-info">
              <h3 class="item-name" :title="item.name">{{ item.name | truncate(15) }}</h3>
              <p class="item-introduce" :title="item.introduce">{{ item.introduce | truncate(40) }}</p>
              <el-button
                type="primary"
                plain
                size="small"
                class="item-action-button"
                @click="navigateToAttractionDetail(item.id)"
              >
                查看与预约
              </el-button>
            </div>
          </el-card>
        </div>
        <el-empty v-else description="暂无推荐景点" class="section-empty"></el-empty>
        <div class="section-more-button-wrapper" v-if="recommendedAttractions.length > 0">
          <el-button size="small" type="primary" plain @click="navigateToAttractionsPage">查看更多景点</el-button>
        </div>
      </section>

      <section class="content-section visitor-stats-banner" v-if="totalAnnualVisitors > 0">
        <p>全年总接待游客量：<span class="visitor-count">{{ totalAnnualVisitors | numberFormat }}</span> 人次</p>
      </section>
      
      <section class="content-section recommended-lines">
        <h2 class="section-title">旅游路线推荐</h2>
        <div class="item-grid" v-if="recommendedLines.length > 0">
           <el-card
            shadow="hover"
            class="item-card line-item-card"
            v-for="item in recommendedLines"
            :key="item.id"
            :body-style="{ padding: '0px' }"
          >
            <el-image
              class="item-image"
              :src="item.images && item.images.split(',')[0]"
              fit="cover"
              lazy
            >
                <div slot="placeholder" class="image-slot loading-slot">加载中...</div>
                <div slot="error" class="image-slot error-slot"><i class="el-icon-picture-outline"></i></div>
            </el-image>
            <div class="item-info">
              <h3 class="item-name" :title="item.name">{{ item.name | truncate(15) }}</h3>
              <p class="item-introduce" :title="item.introduce">{{ item.introduce | truncate(40) }}</p>
              <el-button
                type="success" 
                plain
                size="small"
                class="item-action-button"
                @click="navigateToLineDetail(item.id)"
              >
                查看路线详情
              </el-button>
            </div>
          </el-card>
        </div>
        <el-empty v-else description="暂无旅游路线推荐" class="section-empty"></el-empty>
         <div class="section-more-button-wrapper" v-if="recommendedLines.length > 0">
          <el-button size="small" type="primary" plain @click="navigateToLinesPage">查看更多路线</el-button>
        </div>
      </section>
    </div>

    <bottoms />
  </div>
</template>
<script>
import { 
  getSysRotationsList, 
  getSysAttractionsIndex, 
  getUserCount, // 假设这个API返回的是年度游客总量
  getSysLineIndex 
} from '../../api/api'; // 确保api路径正确
import headers from '@/components/header';
import bottoms from '@/components/bottom';

export default {
  name: 'HomePage', // 1. 组件命名
  components: {
    headers,
    bottoms,
  },
  data() {
    return {
      rotations: [],
      recommendedAttractions: [], // 2. 变量名更清晰
      totalAnnualVisitors: 0,   // 2. 变量名更清晰
      recommendedLines: [],     // 2. 变量名更清晰
      isLoading: { // 3. 为不同板块增加加载状态
        rotations: false,
        attractions: false,
        visitorCount: false,
        lines: false,
      }
    };
  },
  filters: { // 4. 文字截断过滤器
    truncate(text, length, suffix = '...') {
      if (typeof text !== 'string') return '';
      if (text.length > length) {
        return text.substring(0, length) + suffix;
      }
      return text;
    },
    numberFormat(value) { // 5. 数字千位分隔过滤器
      if (value === undefined || value === null) return 'N/A';
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
  },
  methods: {
    // --- 导航方法 ---
    navigateToAttractionsPage() {
      this.$router.push("/attractions");
    },
    navigateToLinesPage() {
      this.$router.push("/line");
    },
    navigateToAttractionDetail(id) { // 6. 使用对象形式跳转
      this.$router.push({ path: "/attractionsInfo", query: { id } });
    },
    navigateToLineDetail(id) { // 6. 使用对象形式跳转
      this.$router.push({ path: "/lineInfo", query: { id } });
    },

    // --- 数据获取方法 (使用 async/await 和错误处理) ---
    async fetchRotations() {
      this.isLoading.rotations = true;
      try {
        const res = await getSysRotationsList();
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.rotations = res.data;
        } else {
          this.$message.error(res.message || '获取轮播图数据失败');
        }
      } catch (error) {
        console.error("获取轮播图数据异常:", error);
        this.$message.error('网络繁忙，轮播图加载失败');
      } finally {
        this.isLoading.rotations = false;
      }
    },
    async fetchRecommendedAttractions() {
      this.isLoading.attractions = true;
      try {
        const res = await getSysAttractionsIndex(); // 假设此API返回少量推荐数据
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.recommendedAttractions = res.data;
        } else {
          this.$message.error(res.message || '获取推荐景点数据失败');
        }
      } catch (error) {
        console.error("获取推荐景点数据异常:", error);
        this.$message.error('网络繁忙，推荐景点加载失败');
      } finally {
        this.isLoading.attractions = false;
      }
    },
    async fetchTotalAnnualVisitors() {
      this.isLoading.visitorCount = true;
      try {
        const res = await getUserCount();
        if (res && (res.code === 1000 || res.code === 200) && res.data !== undefined) {
          this.totalAnnualVisitors = res.data;
        } else {
          this.$message.error(res.message || '获取游客数量失败');
        }
      } catch (error) {
        console.error("获取游客数量异常:", error);
        this.$message.error('网络繁忙，游客数量加载失败');
      } finally {
        this.isLoading.visitorCount = false;
      }
    },
    async fetchRecommendedLines() {
      this.isLoading.lines = true;
      try {
        const res = await getSysLineIndex(); // 假设此API返回少量推荐数据
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.recommendedLines = res.data;
        } else {
          this.$message.error(res.message || '获取旅游路线数据失败');
        }
      } catch (error) {
        console.error("获取旅游路线数据异常:", error);
        this.$message.error('网络繁忙，旅游路线加载失败');
      } finally {
        this.isLoading.lines = false;
      }
    },

    // 初始化所有数据
    loadAllData() {
      this.fetchRotations();
      this.fetchRecommendedAttractions();
      this.fetchTotalAnnualVisitors();
      this.fetchRecommendedLines();
    }
  },
  created() {
    // 可以在这里调用，或者在 mounted 中
  },
  mounted() {
    this.loadAllData(); // 7. 在 mounted 中统一调用数据加载
  }
};
</script>
<style scoped lang="scss">
// === 现代化设计变量 ===
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$success-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
$warning-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$info-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

$text-color-primary: #1f2937;
$text-color-regular: #4b5563;
$text-color-secondary: #6b7280;
$text-color-light: #9ca3af;

$bg-color-page: #f8fafc;
$bg-color-section: #ffffff;

$border-color-light: #e5e7eb;
$border-color-base: #d1d5db;

$card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
$card-hover-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
$card-border-radius: 16px;

$font-family-base: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;

// === 页面容器现代化设计 ===
.home-page {
  font-family: $font-family-base;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
  position: relative;

  // 添加装饰性背景元素
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 600px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    z-index: -1;
  }
}

// === 轮播图现代化设计 ===
.home-carousel {
  margin-bottom: 40px;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  .carousel-image {
    width: 100%;
    height: 100%;
    display: block;
    transition: transform 0.3s ease;
  }

  // 轮播图指示器现代化样式
  ::v-deep .el-carousel__indicators {
    bottom: 20px;

    .el-carousel__indicator {
      button {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.4);
        border: 2px solid rgba(255, 255, 255, 0.6);
        transition: all 0.3s ease;
      }

      &.is-active button {
        background-color: white;
        transform: scale(1.2);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }
    }
  }

  // 轮播图箭头样式
  ::v-deep .el-carousel__arrow {
    background-color: rgba(255, 255, 255, 0.9);
    color: $text-color-primary;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    transition: all 0.3s ease;

    &:hover {
      background-color: white;
      transform: scale(1.1);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
  }
}

.image-slot { // el-image 的占位符和错误提示样式
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f0f2f5; // 更淡的背景
  color: $text-color-secondary;
  font-size: 14px;
  text-align: center;

  &.loading-slot i { // 如果用el-icon-loading
    font-size: 28px;
    color: #667eea;
    animation: rotating 2s linear infinite;
  }
  &.error-slot i {
    font-size: 32px;
    margin-bottom: 10px;
  }
}
@keyframes rotating {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


// === 主内容区域现代化设计 ===
.home-content-wrapper {
  width: 90%;
  max-width: 1400px;
  margin: 0 auto 40px auto;
  padding: 0 20px;
  box-sizing: border-box;
  position: relative;
  z-index: 1;
}

// === 内容板块现代化设计 ===
.content-section {
  background: $bg-color-section;
  padding: 32px 40px;
  margin-bottom: 40px;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  // 添加装饰性渐变边框
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: $primary-gradient;
    border-radius: $card-border-radius $card-border-radius 0 0;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: $card-hover-shadow;
  }

  .section-title {
    font-size: 28px;
    font-weight: 700;
    color: $text-color-primary;
    margin: 0 0 32px 0;
    text-align: center;
    position: relative;

    // 现代化标题装饰
    &::after {
      content: '';
      position: absolute;
      bottom: -12px;
      left: 50%;
      transform: translateX(-50%);
      width: 60px;
      height: 4px;
      background: $primary-gradient;
      border-radius: 2px;
    }
  }

  .section-more-button-wrapper {
    text-align: center;
    margin-top: 32px;

    .el-button {
      padding: 12px 32px;
      font-size: 16px;
      border-radius: 25px;
      background: $primary-gradient;
      border: none;
      color: white;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
      }
    }
  }

  .section-empty {
    padding: 60px 0;

    .el-empty__image {
      width: 120px;
    }

    .el-empty__description {
      font-size: 18px;
      color: $text-color-secondary;
      margin-top: 16px;
    }
  }
}

// === 项目网格现代化设计 ===
.item-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 32px;
  margin-top: 24px;
}

.item-card {
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  height: 100%;
  background: white;
  border: 1px solid rgba(255, 255, 255, 0.8);
  overflow: hidden;
  position: relative;

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: $card-hover-shadow;

    .item-image {
      transform: scale(1.05);
    }

    .item-action-button {
      background: $primary-gradient;
      color: white;
      border: none;
    }
  }

  .item-image {
    width: 100%;
    height: 220px;
    display: block;
    transition: transform 0.4s ease;
    position: relative;

    // 添加图片遮罩效果
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::after {
      opacity: 1;
    }
  }

  .item-info {
    padding: 20px 24px 24px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
  }

  .item-name {
    font-size: 18px;
    font-weight: 600;
    color: $text-color-primary;
    margin: 0 0 12px 0;
    line-height: 1.4;
    min-height: calc(1.4em * 2);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    transition: color 0.3s ease;
  }

  .item-introduce {
    font-size: 14px;
    color: $text-color-regular;
    line-height: 1.6;
    margin-bottom: 20px;
    flex-grow: 1;
    min-height: calc(1.6em * 3);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }

  .item-action-button {
    width: 100%;
    margin-top: auto;
    padding: 12px 0;
    border-radius: 8px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    background: transparent;

    &.el-button--primary {
      color: #667eea;
      border-color: #667eea;

      &:hover {
        background: $primary-gradient;
        color: white;
        border-color: transparent;
        transform: translateY(-1px);
      }
    }

    &.el-button--success {
      color: #11998e;
      border-color: #11998e;

      &:hover {
        background: $success-gradient;
        color: white;
        border-color: transparent;
        transform: translateY(-1px);
      }
    }
  }
}

// === 游客统计横幅现代化设计 ===
.visitor-stats-banner {
  background: $primary-gradient;
  color: white;
  text-align: center;
  font-size: 20px;
  font-weight: 500;
  border-radius: $card-border-radius;
  padding: 40px 32px;
  margin-bottom: 40px;
  box-shadow: $card-shadow;
  position: relative;
  overflow: hidden;

  // 添加装饰性背景图案
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.05) 0%, transparent 70%);
    animation: float 8s ease-in-out infinite reverse;
  }

  p {
    margin: 0;
    position: relative;
    z-index: 1;
  }

  .visitor-count {
    font-size: 36px;
    font-weight: 700;
    margin-left: 12px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    display: inline-block;
    animation: pulse 2s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

// --- 移除 .index10 (空div) ---
// .index10 { /* ... */ }

// --- 移除 .index11 (已整合到 visitor-stats-banner) ---
// .index11 { /* ... */ }

// === 响应式设计优化 ===
@media (max-width: 1200px) {
  .item-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .home-content-wrapper {
    width: 95%;
    padding: 0 16px;
  }

  .content-section {
    padding: 24px 20px;
    margin-bottom: 24px;

    .section-title {
      font-size: 24px;
      margin-bottom: 24px;

      &::after {
        width: 40px;
        height: 3px;
      }
    }

    .section-more-button-wrapper .el-button {
      padding: 10px 24px;
      font-size: 14px;
    }
  }

  .item-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .item-card {
    .item-image {
      height: 200px;
    }

    .item-info {
      padding: 16px 20px 20px;
    }

    .item-name {
      font-size: 16px;
    }

    .item-introduce {
      font-size: 13px;
    }
  }

  .visitor-stats-banner {
    font-size: 18px;
    padding: 32px 24px;

    .visitor-count {
      font-size: 28px;
      display: block;
      margin: 8px 0 0 0;
    }
  }

  .home-carousel {
    margin-bottom: 24px;
    border-radius: 12px;

    ::v-deep .el-carousel__arrow {
      width: 40px;
      height: 40px;
    }
  }
}

@media (max-width: 480px) {
  .home-content-wrapper {
    width: 100%;
    padding: 0 12px;
  }

  .content-section {
    padding: 20px 16px;
    border-radius: 12px;

    .section-title {
      font-size: 20px;
    }
  }

  .item-card .item-info {
    padding: 12px 16px 16px;
  }

  .visitor-stats-banner {
    font-size: 16px;
    padding: 24px 16px;

    .visitor-count {
      font-size: 24px;
    }
  }
}
</style>