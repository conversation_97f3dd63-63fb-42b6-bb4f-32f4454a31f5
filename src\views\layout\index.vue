<template>
  <div class="modern-layout">
    <div class="layout-container">
      <!-- 现代化侧边栏 -->
      <el-asside class="modern-sidebar"></el-asside>

      <!-- 主内容区域 -->
      <div class="main-content">
        <!-- 现代化头部 -->
        <el-header class="modern-header"></el-header>

        <!-- 面包屑导航 -->
        <div class="breadcrumb-container">
          <el-breadcrumb separator="/" class="modern-breadcrumb">
            <el-breadcrumb-item>
              <i class="el-icon-s-home"></i>
              首页
            </el-breadcrumb-item>
            <el-breadcrumb-item v-if="$route.name !== '首页'">
              {{ $route.name }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>

        <!-- 现代化标签页导航 -->
        <div class="tabs-navigation" v-if="activeMenuArrary.length > 1">
          <div class="tabs-scroll-container">
            <div class="scroll-button left" @click="leftScroll()" v-show="showScrollButtons">
              <i class="el-icon-arrow-left"></i>
            </div>

            <div class="tabs-container" id="tabsContainer" ref="tabsContainer">
              <div
                class="tab-item"
                v-for="(item, index) in activeMenuArrary"
                :key="index"
                :class="{ 'active': item.url === activeMenu }"
                @click="openMenu(item.url)"
              >
                <span class="tab-name">{{ item.name }}</span>
                <i
                  v-if="item.url !== '/index'"
                  class="tab-close el-icon-close"
                  @click.stop="closeMenu(item.url)"
                ></i>
              </div>
            </div>

            <div class="scroll-button right" @click="rightScroll()" v-show="showScrollButtons">
              <i class="el-icon-arrow-right"></i>
            </div>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="content-wrapper">
          <el-main class="modern-main"></el-main>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { mapState } from 'vuex'
  import elAsside from "../../components/layout/aside"
  import elMain from "../../components/layout/main"
  import elHeader from "../../components/layout/header"

  export default {
    name: "ModernLayout",
    data() {
      return {
        showScrollButtons: false,
        scrollPosition: 0
      }
    },
    components: {
      elAsside,
      elMain,
      elHeader
    },
    computed: {
      ...mapState({
        activeMenuArrary: state => state.menu.activeMenuArrary,
        activeMenu: state => state.menu.activeMenu
      })
    },
    methods: {
      // 平滑滚动到左侧
      leftScroll() {
        const container = this.$refs.tabsContainer;
        if (container) {
          container.scrollTo({
            left: container.scrollLeft - 200,
            behavior: 'smooth'
          });
        }
      },

      // 平滑滚动到右侧
      rightScroll() {
        const container = this.$refs.tabsContainer;
        if (container) {
          container.scrollTo({
            left: container.scrollLeft + 200,
            behavior: 'smooth'
          });
        }
      },

      // 检查是否需要显示滚动按钮
      checkScrollButtons() {
        const container = this.$refs.tabsContainer;
        if (container) {
          this.showScrollButtons = container.scrollWidth > container.clientWidth;
        }
      },

      // 打开菜单标签页
      openMenu(url) {
        if (this.activeMenu !== url) {
          // 添加页面切换动画
          this.animatePageTransition(() => {
            this.$router.push({ path: url });
            this.$store.commit('menu/setActiveMenu', url);
          });
        }
      },

      // 关闭菜单标签页
      closeMenu(url) {
        let index = this.activeMenuArrary.length - 1;
        for (let i = 0; i < this.activeMenuArrary.length; i++) {
          if (this.activeMenuArrary[i].url === url) {
            index = i;
            break;
          }
        }

        if (this.activeMenu === url && index > 0) {
          const targetUrl = this.activeMenuArrary[index - 1].url;
          this.animatePageTransition(() => {
            this.$router.push({ path: targetUrl });
            this.$store.commit('menu/setActiveMenu', targetUrl);
          });
        }

        this.$store.commit('menu/reduceActiveMenu', index);
        this.$bus.$emit("clearKeepAlive", url);

        // 重新检查滚动按钮
        this.$nextTick(() => {
          this.checkScrollButtons();
        });
      },

      // 页面切换动画
      animatePageTransition(callback) {
        const mainContent = document.querySelector('.content-wrapper');
        if (mainContent) {
          mainContent.style.opacity = '0.7';
          mainContent.style.transform = 'translateY(10px)';

          setTimeout(() => {
            callback();
            setTimeout(() => {
              mainContent.style.opacity = '1';
              mainContent.style.transform = 'translateY(0)';
            }, 50);
          }, 150);
        } else {
          callback();
        }
      }
    },

    mounted() {
      // 检查滚动按钮
      this.$nextTick(() => {
        this.checkScrollButtons();
      });

      // 监听窗口大小变化
      window.addEventListener('resize', this.checkScrollButtons);

      // 监听标签页容器滚动
      const container = this.$refs.tabsContainer;
      if (container) {
        container.addEventListener('scroll', () => {
          this.scrollPosition = container.scrollLeft;
        });
      }
    },

    beforeDestroy() {
      window.removeEventListener('resize', this.checkScrollButtons);
    }
  }
</script>
</script>

<style lang="scss" scoped>
  .modern-layout {
    height: 100vh;
    background: var(--bg-primary);
    overflow: hidden;
  }

  .layout-container {
    display: flex;
    height: 100%;
    position: relative;
  }

  .modern-sidebar {
    flex-shrink: 0;
    transition: all var(--transition-normal) ease;
    z-index: 100;
  }

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: var(--bg-primary);
  }

  .modern-header {
    flex-shrink: 0;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--shadow-sm);
    z-index: 90;
  }

  // 面包屑导航样式
  .breadcrumb-container {
    padding: 12px 24px;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);

    .modern-breadcrumb {
      font-size: 14px;

      :deep(.el-breadcrumb__item) {
        .el-breadcrumb__inner {
          color: var(--text-secondary);
          font-weight: 500;
          transition: color var(--transition-fast) ease;

          &:hover {
            color: var(--primary-color);
          }

          i {
            margin-right: 6px;
            color: var(--primary-color);
          }
        }

        &:last-child .el-breadcrumb__inner {
          color: var(--text-primary);
          font-weight: 600;
        }
      }
    }
  }

  // 现代化标签页导航
  .tabs-navigation {
    background: var(--glass-bg);
    backdrop-filter: blur(15px);
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 80;
  }

  .tabs-scroll-container {
    display: flex;
    align-items: center;
    height: 48px;
    position: relative;
  }

  .scroll-button {
    width: 40px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-right: 1px solid var(--glass-border);
    color: var(--text-secondary);
    transition: all var(--transition-fast) ease;
    z-index: 10;

    &:hover {
      background: var(--primary-gradient);
      color: white;
      transform: scale(1.05);
    }

    &.left {
      border-right: 1px solid var(--glass-border);
    }

    &.right {
      border-left: 1px solid var(--glass-border);
    }

    i {
      font-size: 14px;
    }
  }

  .tabs-container {
    flex: 1;
    display: flex;
    align-items: center;
    overflow-x: auto;
    overflow-y: hidden;
    scroll-behavior: smooth;
    padding: 0 8px;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .tab-item {
    display: flex;
    align-items: center;
    padding: 0 16px;
    height: 36px;
    margin: 0 4px;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    cursor: pointer;
    transition: all var(--transition-normal) ease;
    white-space: nowrap;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: var(--primary-gradient);
      transition: left var(--transition-normal) ease;
      z-index: -1;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
      border-color: var(--primary-color);

      &::before {
        left: 0;
      }

      .tab-name {
        color: white;
      }

      .tab-close {
        color: white;
        background: rgba(255, 255, 255, 0.2);
      }
    }

    &.active {
      background: var(--primary-gradient);
      color: white;
      box-shadow: var(--shadow-md);
      transform: translateY(-1px);

      .tab-name {
        color: white;
        font-weight: 600;
      }

      .tab-close {
        color: white;
        background: rgba(255, 255, 255, 0.2);

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.1);
        }
      }
    }
  }

  .tab-name {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
    transition: color var(--transition-fast) ease;
    margin-right: 8px;
  }

  .tab-close {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    color: var(--text-secondary);
    background: rgba(0, 0, 0, 0.1);
    transition: all var(--transition-fast) ease;

    &:hover {
      background: rgba(255, 0, 0, 0.2);
      color: #ff4757;
      transform: scale(1.1);
    }
  }

  // 主内容区域
  .content-wrapper {
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .modern-main {
    height: 100%;
    padding: 24px;
    background: transparent;
    transition: all var(--transition-normal) ease;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--primary-gradient);
      border-radius: 3px;

      &:hover {
        background: var(--secondary-gradient);
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .breadcrumb-container {
      padding: 8px 16px;
    }

    .modern-main {
      padding: 16px;
    }

    .tab-item {
      padding: 0 12px;
      margin: 0 2px;
    }

    .scroll-button {
      width: 32px;
    }
  }

  // 全局菜单项样式覆盖
  :deep(.el-menu-item) {
    min-width: 0;
  }
</style>