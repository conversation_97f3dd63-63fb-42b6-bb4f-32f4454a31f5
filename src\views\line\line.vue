<template>
  <div class="line-page"> 
    <headers />

    <div class="line-main-content">
      <el-card class="search-panel-line" shadow="never">
        <el-form :inline="true" :model="searchParams" size="small" class="search-form">
          <el-form-item label="线路名称">
            <el-input v-model="searchParams.name" placeholder="请输入线路名称" clearable />
          </el-form-item>
          <el-form-item label="地理情况">
            <el-input v-model="searchParams.geography" placeholder="如：山区、沿海" clearable />
          </el-form-item>
          <el-form-item label="适宜温度">
            <el-input v-model="searchParams.temperature" placeholder="如：20℃-25℃" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearchForm" icon="el-icon-refresh">重置</el-button> 
          </el-form-item>
        </el-form>
      </el-card>

      <div v-loading="isLoading" class="list-container-line">
        <el-row :gutter="20" class="line-list-row" v-if="linesList.length > 0">
          <el-col
            :xs="24" :sm="12" :md="8" 
            v-for="item in linesList"
            :key="item.id"
            class="line-col"
          >
            <el-card 
              shadow="hover" 
              class="line-card" 
              :body-style="{ padding: '0px' }"
              @click.native="navigateToDetail(item.id)" 
            >
              <el-image
                class="line-image"
                :src="item.images && item.images.split(',')[0]"
                fit="cover"
                lazy
              >
                <div slot="placeholder" class="image-slot loading">
                  <i class="el-icon-loading"></i>
                </div>
                <div slot="error" class="image-slot error">
                  <i class="el-icon-picture-outline"></i> <span>图片加载失败</span>
                </div>
              </el-image>
              <div class="line-info">
                <h3 class="line-name" :title="item.name">{{ item.name | truncate(25) }}</h3>
                <p class="line-introduce" :title="item.introduce">
                  {{ item.introduce | truncate(50) }}
                </p>
                </div>
            </el-card>
          </el-col>
        </el-row>
        <el-empty v-else description="暂无相关旅游线路" class="line-empty"></el-empty>
      </div>

      <el-pagination
        v-if="totalItems > 0 && linesList.length > 0"
        class="line-pagination"
        background
        :current-page.sync="searchParams.pageNumber"
        :page-size="searchParams.pageSize"
        layout="total, prev, pager, next, jumper"
        :total="totalItems"
        @current-change="handlePageChange"
      />
    </div>
    <bottoms />
  </div>
</template>

<script>
import { getSysLinePage } from '../../api/api'; // 确保API路径和函数名正确
import headers from '@/components/header';
import bottoms from '@/components/bottom';

export default {
  name: 'LineListPage', // 1. 组件命名
  components: {
    headers,
    bottoms,
  },
  data() {
    return {
      searchParams: { // 2. 变量名优化
        name: '',
        geography: '',
        temperature: '',
        pageSize: 12,
        pageNumber: 1,
      },
      totalItems: 0,   // 2. 变量名优化
      linesList: [],   // 2. 变量名优化
      isLoading: false, // 3. 加载状态
    };
  },
  filters: { // 4. 文字截断过滤器
    truncate(text, length, suffix = '...') {
      if (typeof text !== 'string') return '';
      if (text.length > length) {
        return text.substring(0, length) + suffix;
      }
      return text;
    },
  },
  methods: {
    // 5. API调用封装和错误处理
    async fetchLinesData() {
      this.isLoading = true;
      try {
        const params = { ...this.searchParams };
        const res = await getSysLinePage(params);
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.linesList = res.data.records || [];
          this.totalItems = Number(res.data.total) || 0; // 确保total是数字
        } else {
          this.$message.error(res.message || '获取线路数据失败');
          this.linesList = [];
          this.totalItems = 0;
        }
      } catch (error) {
        console.error('获取线路列表失败:', error);
        this.$message.error('网络繁忙，请稍后重试');
        this.linesList = [];
        this.totalItems = 0;
      } finally {
        this.isLoading = false;
      }
    },
    // 6. 搜索与重置方法名优化
    handleSearch() {
      this.searchParams.pageNumber = 1; // 搜索时重置到第一页
      this.fetchLinesData();
    },
    resetSearchForm() {
      this.searchParams.name = '';
      this.searchParams.geography = '';
      this.searchParams.temperature = '';
      this.searchParams.pageNumber = 1;
      this.fetchLinesData();
    },
    // 7. 导航方法名优化
    navigateToDetail(id) {
      this.$router.push({ path: '/lineInfo', query: { id } });
    },
    // 8. 分页处理方法名优化
    handlePageChange(page) {
      this.searchParams.pageNumber = page;
      this.fetchLinesData();
    },
  },
  created() {
    this.fetchLinesData(); // 9. 组件创建时加载数据
  },
};
</script>

<style scoped lang="scss">
// --- SCSS Variables (建议定义在全局或单独的variables.scss中引入) ---
$primary-color: #409EFF;
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$border-color-light: #e4e7ed;
$bg-color-page: #f5f7fa;
$card-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.06);
$card-hover-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
$card-border-radius: 8px;
$font-family-base: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;

// --- Page Container ---
.line-page {
  font-family: $font-family-base;
  background-color: $bg-color-page;
  min-height: calc(100vh - 120px); // 假设 header 和 bottom 各占 60px
  display: flex;
  flex-direction: column;
}

.line-main-content {
  width: 85%;
  max-width: 1300px;
  margin: 20px auto;
  flex-grow: 1;
}

// --- Search Panel ---
.search-panel-line {
  margin-bottom: 25px;
  border-radius: $card-border-radius;
  padding: 5px 15px; // 调整内边距使表单更紧凑

  .search-form {
    .el-form-item {
      margin-bottom: 0; // 移除 el-form-item 默认的下边距，以适应行内表单
      margin-right: 15px; // 表单项之间的右边距
      &:last-child {
        margin-right: 0;
      }
    }
    .el-input { // 直接控制输入框宽度
      width: 220px; 
    }
  }
}

// --- List Container ---
.list-container-line {
  min-height: 300px; // 加载状态最小高度
}

// --- Line Card ---
.line-col {
  margin-bottom: 20px;
}

.line-card {
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  transition: transform 0.25s ease-in-out, box-shadow 0.25s ease-in-out;
  height: 100%; // 使卡片在栅格中等高
  display: flex;
  flex-direction: column;
  cursor: pointer; // 让整个卡片可点击

  &:hover {
    transform: translateY(-4px);
    box-shadow: $card-hover-shadow;
  }

  .line-image { // el-image
    width: 100%;
    height: 180px;
    display: block;
    background-color: #eee;
  }

  .image-slot { /* 与 attractions.vue 中样式类似 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: #f5f7fa;
    color: $text-color-secondary;
    font-size: 13px;
    text-align: center;

    &.loading i { font-size: 24px; color: $primary-color; animation: rotating 2s linear infinite; }
    &.error i { font-size: 28px; margin-bottom: 8px; }
  }
  @keyframes rotating { /* ... */ }


  .line-info {
    padding: 12px 15px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    text-align: left;
  }

  .line-name {
    font-size: 16px;
    color: $text-color-primary;
    font-weight: 500;
    margin: 0 0 6px 0;
    line-height: 1.4;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    min-height: calc(1.4em * 2); 
  }

  .line-introduce {
    font-size: 13px;
    color: $text-color-regular;
    line-height: 1.5;
    flex-grow: 1; // 使得介绍可以撑开空间，把按钮推到底部（如果按钮存在且不是绝对定位）
    margin-bottom: 10px; // 如果下方没有按钮，则这是与卡片底部的间距
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3; 
    -webkit-box-orient: vertical;
    min-height: calc(1.5em * 3); 
  }
  // 如果需要在卡片底部添加价格或按钮，可以取消注释并调整
  /*
  .line-price {
    font-size: 16px;
    color: #FF4949;
    font-weight: bold;
    margin-top: auto; // 将价格信息推到底部
  }
  */
}

// --- Empty State ---
.line-empty {
  width: 100%;
  padding: 30px 0;
  min-height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

// --- Pagination ---
.line-pagination {
  margin-top: 30px;
  padding-bottom: 20px;
  text-align: center;
}
</style>