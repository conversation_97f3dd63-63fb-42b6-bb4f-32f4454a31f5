<template>
  <div class="line-detail-page">
    <headers />

    <div v-loading="isLoading.page" class="line-detail-main-content">
      <el-card v-if="lineDetail.id" class="content-card" shadow="never">
        <div class="carousel-section" v-if="imageUrls.length > 0">
          <el-carousel height="450px" class="line-carousel">
            <el-carousel-item v-for="(imgUrl, index) in imageUrls" :key="index">
              <el-image class="carousel-image" :src="imgUrl" fit="contain" > {/* fit改为contain或cover，根据图片调整 */}
                <div slot="placeholder" class="image-slot loading"><i class="el-icon-loading"></i></div>
                <div slot="error" class="image-slot error"><i class="el-icon-picture-outline"></i></div>
              </el-image>
            </el-carousel-item>
          </el-carousel>
        </div>
        <el-empty v-else description="暂无展示图片" class="image-empty-placeholder"></el-empty>

        <div class="title-actions-section">
          <h1 class="line-title">{{ lineDetail.name }}</h1>
          <el-button 
            size="small" 
            :type="isFavorited ? 'warning' : 'success'" 
            :icon="isFavorited ? 'el-icon-star-off' : 'el-icon-star-on'" 
            circle
            @click="toggleFavoriteStatus"
            :loading="isLoading.favor"
            :title="isFavorited ? '取消收藏' : '点击收藏'"
          />
        </div>

        <div class="line-content-html" v-html="lineDetail.content"></div>
      </el-card>
      <el-empty v-else-if="!isLoading.page" description="线路详情加载失败或不存在" class="page-empty-placeholder"></el-empty>
    </div>

    <bottoms />
  </div>
</template>

<script>
import { getSysLineById, getSysFavor, saveSysFavor, removeSysFavor } from '../../api/api'; // 确认API路径
import headers from '@/components/header';
import bottoms from '@/components/bottom';

export default {
  name: 'LineDetailPage',
  components: { headers, bottoms },
  data() {
    return {
      lineId: null, // 从路由获取
      isLoading: {
        page: true,
        favor: false,
      },
      lineDetail: { // 初始化数据结构，避免模板渲染错误
        id: null,
        images: '',
        name: '',
        content: '<p>内容加载中...</p>', // 初始占位内容
      },
      favorData: null, // 存储收藏信息对象，包含id等
    };
  },
  computed: {
    imageUrls() {
      if (this.lineDetail.images && typeof this.lineDetail.images === 'string') {
        return this.lineDetail.images.split(',').map(imgUrl => {
          // 假设图片路径已经是完整的URL或者需要拼接HOST
          // 如果 item 已经是完整 URL, 不需要拼接 $store.state.HOST
          // 如果是相对路径且 HOST 在Vuex中，则: this.$store.state.HOST + imgUrl
          // 此处假设 item 直接是可访问的 URL
          return imgUrl.trim(); 
        }).filter(url => url); // 过滤掉空字符串
      }
      return [];
    },
    isFavorited() {
      return !!this.favorData && !!this.favorData.id; // 根据 favorData.id 是否存在判断
    }
  },
  methods: {
    async fetchLineDetail() {
      if (!this.lineId) return;
      this.isLoading.page = true;
      try {
        const res = await getSysLineById({ id: this.lineId });
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.lineDetail = res.data;
        } else {
          this.$message.error(res.message || '获取线路详情失败');
          this.lineDetail.content = '<p>线路详情加载失败。</p>';
        }
      } catch (error) {
        console.error("获取线路详情异常:", error);
        this.$message.error('网络错误，线路详情加载失败');
        this.lineDetail.content = '<p>网络错误，线路详情加载失败。</p>';
      } finally {
        this.isLoading.page = false;
      }
    },
    async fetchFavorStatus() {
      if (!this.lineId) return;
      // 注意：getSysFavor 接口参数通常需要 userId 和 targetId (lineId)
      // 此处假设接口 getSysFavor({id: this.lineId}) 是根据线路ID判断当前用户是否收藏了该线路
      // 如果接口是获取用户的所有收藏，则逻辑需要调整
      this.isLoading.favor = true;
      try {
        // 实际的 getSysFavor 接口参数可能需要 userId
        // const userInfo = JSON.parse(window.localStorage.getItem("user_info"));
        // if (!userInfo || !userInfo.id) { this.favorData = null; return; }
        // const params = { lineId: this.lineId, userId: userInfo.id };
        const res = await getSysFavor({ id: this.lineId }); // 假设id是lineId，后端会根据token判断用户
        if (res && (res.code === 1000 || res.code === 200) && res.data && res.data.id) {
          this.favorData = res.data; // res.data 应该是收藏记录对象，包含收藏ID
        } else {
          this.favorData = null; // 表示未收藏或获取失败
        }
      } catch (error) {
        console.error("获取收藏状态异常:", error);
        this.favorData = null; 
      } finally {
        this.isLoading.favor = false;
      }
    },
    async toggleFavoriteStatus() {
      if (!this.lineId) return;
      this.isLoading.favor = true;
      try {
        if (this.isFavorited && this.favorData && this.favorData.id) { // 取消收藏
          const res = await removeSysFavor({ ids: this.favorData.id }); // 假设 ids 是后端期望的参数
          if (res && (res.code === 1000 || res.code === 200)) {
            this.$message.success('取消收藏成功');
            this.favorData = null; // 更新状态
          } else {
            this.$message.error(res.message || '取消收藏失败');
          }
        } else { // 添加收藏
          const params = { lineId: this.lineId }; // 假设后端需要 lineId
          const res = await saveSysFavor(params);
          if (res && (res.code === 1000 || res.code === 200) && res.data) {
            this.$message.success('收藏成功');
            this.favorData = res.data; // 更新状态，假设后端返回新的收藏对象
          } else {
            this.$message.error(res.message || '收藏失败');
          }
        }
      } catch (error) {
        console.error("操作收藏失败:", error);
        this.$message.error('操作失败，请稍后重试');
      } finally {
        this.isLoading.favor = false;
      }
    },
    async initializePage() {
      this.lineId = this.$route.query.id;
      if (this.lineId) {
        // 并行加载页面详情和收藏状态
        this.isLoading.page = true; // 统一控制页面加载状态
        await Promise.all([
          this.fetchLineDetail(),
          this.fetchFavorStatus() 
        ]);
        this.isLoading.page = false;
      } else {
        this.$message.error('无效的线路ID');
        this.isLoading.page = false;
        // this.$router.replace('/line'); // 或其他错误处理
      }
    }
  },
  created() {
    this.initializePage();
  },
  watch: {
    // 如果路由ID变化（例如通过浏览器前进后退到不同详情页），重新加载数据
    '$route.query.id'(newId) {
      if (newId && newId !== this.lineId) {
        this.initializePage();
      }
    }
  }
};
</script>

<style scoped lang="scss">
// --- SCSS Variables ---
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C; // 用于已收藏按钮
$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$border-color-light: #e4e7ed;
$bg-color-page: #f5f7fa;
$card-bg-light: #ffffff;
$card-shadow: 0 2px 8px rgba(0,0,0,.05);
$card-border-radius: 6px;

// --- Page Container ---
.line-detail-page {
  font-family: 'Helvetica Neue', Helvetica, /* ... */;
  background-color: $bg-color-page;
  min-height: calc(100vh - 120px); 
  display: flex;
  flex-direction: column;
}

.line-detail-main-content {
  width: 70%; // 内容区宽度调整
  max-width: 960px; // 最大宽度
  margin: 25px auto; // 上下边距增加
  flex-grow: 1;
}

.content-card {
  background-color: $card-bg-light;
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  border: 1px solid $border-color-light;
   ::v-deep .el-card__body {
    padding: 20px 25px; // 卡片内边距
  }
}

// --- Carousel Section ---
.carousel-section {
  margin-bottom: 25px; // 轮播图与下方标题的间距
  .line-carousel {
    border-radius: $card-border-radius - 2px; // 轮播图圆角与卡片协调
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.08); // 轻微阴影
  }
  .carousel-image { width: 100%; height: 100%; display: block; }
  .image-slot { /* ...与之前页面类似... */ 
    display: flex; flex-direction: column; justify-content: center; align-items: center;
    width: 100%; height: 100%; background: #f0f2f5; color: $text-color-secondary;
    font-size: 14px; text-align: center;
    i { font-size: 32px; margin-bottom: 8px; }
  }
}
.image-empty-placeholder, .page-empty-placeholder {
    padding: 40px 0;
    min-height: 200px;
}


// --- Title and Actions ---
.title-actions-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px; // 标题与富文本内容的间距
  padding-bottom: 15px; // 标题底部留白
  border-bottom: 1px solid $border-color-light; // 分割线

  .line-title {
    font-size: 22px; // 标题字号加大
    font-weight: 600;
    color: $text-color-primary;
    margin: 0;
    flex-grow: 1; // 让标题占据主要空间
    padding-right: 15px; // 与收藏按钮的间距
  }
  .el-button { // 收藏按钮
    flex-shrink: 0; // 防止按钮被压缩
    // transition: all 0.2s ease; // 平滑过渡
  }
}

// --- HTML Content Area ---
.line-content-html {
  font-size: 15px; // 富文本内容基础字号
  line-height: 1.8; // 富文本内容行高
  color: $text-color-regular;

  // 针对 v-html 内部常见标签进行样式重置或美化
  // 使用 ::v-deep 或 /deep/ 穿透 scoped 限制
  ::v-deep {
    p {
      margin-bottom: 1em;
      word-wrap: break-word;
    }
    img {
      max-width: 100% !important; // 确保图片响应式
      height: auto !important;
      display: block; // 消除图片下方空白
      margin: 10px auto; // 图片居中并有上下边距
      border-radius: 4px; // 图片圆角
      box-shadow: 0 2px 5px rgba(0,0,0,0.1); // 图片阴影
    }
    h1, h2, h3, h4, h5, h6 {
      color: $text-color-primary;
      margin-top: 1.5em;
      margin-bottom: 0.8em;
      font-weight: 500;
    }
    h1 { font-size: 1.8em; }
    h2 { font-size: 1.6em; }
    h3 { font-size: 1.4em; }
    ul, ol {
      padding-left: 25px;
      margin-bottom: 1em;
    }
    li {
      margin-bottom: 0.5em;
    }
    a {
      color: $primary-color;
      text-decoration: none;
      &:hover {
        text-decoration: underline;
      }
    }
    blockquote {
      margin: 1em 0;
      padding: 0.5em 1em;
      border-left: 4px solid $border-color-light;
      background-color: #f9fafc;
      color: $text-color-regular;
    }
    table {
        width: auto; /* 或者 max-width:100%; overflow-x:auto; */
        border-collapse: collapse;
        margin: 1em 0;
        font-size: 0.95em;
        th, td {
            border: 1px solid $border-color-light;
            padding: 0.5em 0.75em;
            text-align: left;
        }
        th {
            background-color: #fafafa;
            font-weight: 500;
        }
    }
  }
}

// loading动画 (如果需要)
@keyframes rotating { 
  from { transform: rotate(0deg); } 
  to { transform: rotate(360deg); } 
}

</style>