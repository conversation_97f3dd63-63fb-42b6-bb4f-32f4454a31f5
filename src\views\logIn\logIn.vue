<template>
  <div class="login-page-wrapper" :style="{ backgroundImage: 'url(' + loginBgImage + ')' }"> 
   
    
    <div class="login-form-container">
      <el-card class="login-card" shadow="lg">
        <div class="login-header">
          <!-- <img src="../../assets/image/logo.png" alt="Logo" class="login-logo" /> -->
          <h2 class="login-title">欢迎使用旅游推荐平台</h2>
        </div>

        <el-form :model="loginForm" :rules="loginRules" ref="loginFormRef" @submit.native.prevent="handleLogin">
          <el-form-item prop="loginAccount">
            <el-input
              v-model="loginForm.loginAccount"
              placeholder="请输入登录账号"
              prefix-icon="el-icon-user"
              clearable
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              type="password"
              v-model="loginForm.password"
              placeholder="请输入用户密码"
              prefix-icon="el-icon-lock"
              show-password
              @keyup.enter.native="handleLogin"
            />
          </el-form-item>

          <el-form-item class="extra-actions">
            <el-link type="primary" :underline="false" @click="navigateToForgetPassword" class="forgot-password-link">
              忘记密码？
            </el-link>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              class="login-button"
              @click="handleLogin"
              :loading="isLoading"
            >
              登 录
            </el-button>
          </el-form-item>
          <el-form-item>
            <el-button class="register-button" @click="navigateToRegister">
              没有账号？立即注册
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    </div>
</template>

<script>
import { login, getUser } from '../../api/api'; // 确认API路径

import loginBg from '@/assets/image/bg1.jpg'; 
export default {
  name: 'LoginPage',
  data() {
    return {
      loginForm: {
        loginAccount: '',
        password: '',
      },
      loginRules: {
        loginAccount: [{ required: true, message: '请输入登录账号', trigger: 'blur' }],
        password: [
          { required: true, message: '请输入用户密码', trigger: 'blur' },
          { min: 6, message: '密码长度至少为6位', trigger: 'blur' },
        ],
      },
      isLoading: false,
      loginBgImage: loginBg // 2. 将引入的图片赋值给data属性
    };
  },
  methods: {
    navigateToRegister() {
      this.$router.push('/register');
    },
    navigateToForgetPassword() {
      this.$router.push('/forget');
    },
    handleLogin() { // 5. 登录方法优化
      this.$refs.loginFormRef.validate(async (valid) => {
        if (valid) {
          this.isLoading = true;
          try {
            const params = {
              loginAccount: this.loginForm.loginAccount,
              password: this.loginForm.password,
            };
            const loginRes = await login(params);

            console.group('🔍 登录流程调试');
            console.log('登录API响应:', loginRes);
            console.log('响应码:', loginRes?.code);
            console.log('响应数据:', loginRes?.data);
            console.log('Token:', loginRes?.data?.token);

            if (loginRes && (loginRes.code === 1000 || loginRes.code === 200) && loginRes.data && loginRes.data.token) {
                this.$message({
                  message: '登录成功',
                  type: 'success',
                  duration: 3000,  // 显示时间延长到5秒
                  showClose: true  // 添加关闭按钮，让用户可以手动关闭
                });
              const token = loginRes.data.token;
              console.log('✅ 保存Token到localStorage:', token);
              window.localStorage.setItem("user_token", token);

              // 验证Token是否保存成功
              const savedToken = window.localStorage.getItem("user_token");
              console.log('✅ 验证Token保存:', savedToken);

              // 登录成功后立即获取用户信息
              console.log('📡 开始获取用户信息...');
              await this.fetchAndStoreUserInfo();

              // 跳转到首页或其他目标页面
              // 可以考虑是否有重定向参数，例如 this.$route.query.redirect
              const redirect = this.$route.query.redirect || '/';
              console.log('🔄 准备跳转到:', redirect);
              console.groupEnd();
              this.$router.push(redirect);

            } else {
              this.$message.error(loginRes.message || '登录失败，请检查账号或密码');
            }
          } catch (error) {
            console.error('登录API请求异常:', error);
            this.$message.error('登录请求失败，请稍后重试');
          } finally {
            this.isLoading = false;
          }
        } else {
          console.log('登录表单校验失败');
          return false;
        }
      });
    },
    async fetchAndStoreUserInfo() { // 6. 获取并存储用户信息
      try {
        console.log('📡 调用getUser API...');
        const userInfoRes = await getUser();
        console.log('📥 用户信息API响应:', userInfoRes);

        if (userInfoRes && (userInfoRes.code === 1000 || userInfoRes.code === 200) && userInfoRes.data) {
          console.log('✅ 保存用户信息到localStorage:', userInfoRes.data);
          window.localStorage.setItem("user_info", JSON.stringify(userInfoRes.data));

          // 验证用户信息是否保存成功
          const savedUserInfo = window.localStorage.getItem("user_info");
          console.log('✅ 验证用户信息保存:', savedUserInfo);

          // 如果使用Vuex，在这里commit用户信息到store
          // this.$store.commit('user/SET_USER_INFO', userInfoRes.data);
        } else {
           console.warn("❌ 获取用户信息失败:", userInfoRes?.message);
           // 即使获取用户信息失败，也可能允许登录，取决于业务逻辑
        }
      } catch (error) {
        console.error('❌ 获取用户信息API请求异常:', error);
      }
    },
  },
  created() {
    // 可以在这里检查是否已登录，如果已登录则重定向到首页
    if (window.localStorage.getItem("user_token")) {
      // console.log('User already logged in, redirecting...');
      // this.$router.replace('/'); // 使用replace避免用户可以回退到登录页
    }
  },
  mounted() {
    // 可以在此处理回车键登录，但通过 @keyup.enter.native="handleLogin" 在密码框上更直接
  }
};
</script>

<style scoped lang="scss">
// --- 现代化设计系统变量 ---
$primary-color: #1890ff;
$primary-light: #40a9ff;
$primary-dark: #096dd9;
$brand-color: #1890ff;
$text-color-primary: #262626;
$text-color-secondary: #595959;
$text-color-tertiary: #8c8c8c;
$text-color-on-dark-bg: #ffffff;
$bg-primary: #ffffff;
$bg-secondary: #fafafa;
$border-light: #f0f0f0;
$border-base: #d9d9d9;
$login-card-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
$input-height: 44px;
$border-radius-sm: 4px;
$border-radius-base: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;
$font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

// --- 现代化页面布局 ---
.login-page-wrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow: hidden;
  position: relative;

  // 添加动态背景效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="a" cx=".5" cy=".5" r=".5"><stop offset="0" stop-color="%23ffffff" stop-opacity=".1"/><stop offset="1" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="20" cy="20" r="10" fill="url(%23a)"/><circle cx="80" cy="80" r="15" fill="url(%23a)"/><circle cx="40" cy="70" r="8" fill="url(%23a)"/><circle cx="90" cy="30" r="12" fill="url(%23a)"/><circle cx="10" cy="90" r="6" fill="url(%23a)"/></svg>');
    animation: float 20s ease-in-out infinite;
    pointer-events: none;
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.login-form-container {
  position: relative;
  z-index: 10;
}

.login-card {
  width: 420px;
  padding: 40px;
  border-radius: $border-radius-xl;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  box-shadow: $login-card-shadow;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  }

  .login-header {
    text-align: center;
    margin-bottom: 32px;

    .login-logo {
      width: 56px;
      height: 56px;
      border-radius: $border-radius-lg;
      margin-bottom: 16px;
      box-shadow: 0 8px 24px rgba(24, 144, 255, 0.3);
      transition: transform 0.3s ease;

      &:hover {
        transform: scale(1.05) rotate(5deg);
      }
    }

    .login-title {
      font-size: 24px;
      color: $text-color-primary;
      font-weight: 600;
      font-family: $font-family;
      margin: 0;
      background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .el-form-item {
    margin-bottom: 20px;

    ::v-deep .el-input__inner {
      height: $input-height;
      line-height: $input-height;
      border-radius: 4px;
      // 如果表单背景透明度高且背景图复杂，输入框可以加个浅色背景
      // background-color: rgba(255, 255, 255, 0.8); 
      &:focus {
        border-color: $primary-color;
        // box-shadow: 0 0 0 2px rgba($primary-color, 0.2); // 聚焦时的外发光
      }
    }
     ::v-deep .el-input__prefix {
        display: flex;
        align-items: center;
        height: 100%;
        .el-input__icon { color: $text-color-secondary; } // 图标颜色
     }
     // 如果背景是深色透明，placeholder文字颜色也需调整
     // ::v-deep input::placeholder { color: rgba($text-color-on-dark-bg, 0.7); }
  }
  
  .extra-actions {
    margin-bottom: 20px; // 忘记密码与登录按钮的间距调整
    text-align: right;

    .forgot-password-link {
      font-size: 13px;
      // 如果背景是深色透明，链接颜色应改为浅色
      // color: rgba($text-color-on-dark-bg, 0.8);
      color: $text-color-secondary; 
      &:hover {
        // color: $text-color-on-dark-bg;
        color: $primary-color;
      }
    }
  }

  .login-button, .register-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    border-radius: $border-radius-lg;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    font-family: $font-family;
    margin-bottom: 16px;
  }

  .login-button {
    background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
    border: none;
    color: white;
    box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(24, 144, 255, 0.4);
      background: linear-gradient(135deg, $primary-light 0%, $primary-color 100%);
    }

    &:active {
      transform: translateY(0);
    }

    &.is-loading {
      background: linear-gradient(135deg, $text-color-tertiary 0%, $border-base 100%);
    }
  }

  .register-button {
    background: transparent;
    border: 2px solid $border-base;
    color: $text-color-secondary;

    &:hover {
      border-color: $primary-color;
      color: $primary-color;
      background: rgba(24, 144, 255, 0.05);
      transform: translateY(-1px);
    }
  }
}

// --- Login Footer (可选，如果添加) ---
// .login-footer {
//   position: absolute;
//   bottom: 20px;
//   left: 0;
//   right: 0;
//   text-align: center;
//   color: rgba(255, 255, 255, 0.7); // 确保在背景图上可见
//   font-size: 13px;
// }
</style>