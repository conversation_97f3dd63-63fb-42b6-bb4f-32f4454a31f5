<template>
  <div class="login-debug-page">
    <headers />
    
    <div class="debug-content">
      <h1 class="page-title">🔑 登录状态调试工具</h1>
      
      <!-- 当前登录状态 -->
      <section class="debug-section">
        <h2 class="section-title">当前登录状态</h2>
        <div class="status-display">
          <div class="status-item" :class="{ 'success': hasToken, 'error': !hasToken }">
            <i :class="hasToken ? 'el-icon-check' : 'el-icon-close'"></i>
            <span>Token状态: {{ hasToken ? '存在' : '缺失' }}</span>
            <div v-if="hasToken" class="token-preview">
              <small>{{ tokenPreview }}</small>
            </div>
          </div>
          
          <div class="status-item" :class="{ 'success': hasUserInfo, 'error': !hasUserInfo }">
            <i :class="hasUserInfo ? 'el-icon-check' : 'el-icon-close'"></i>
            <span>用户信息: {{ hasUserInfo ? '存在' : '缺失' }}</span>
            <div v-if="hasUserInfo" class="user-preview">
              <small>用户名: {{ userInfo.user_name || '未设置' }}</small>
            </div>
          </div>
          
          <div class="status-item" :class="{ 'success': isLoggedIn, 'error': !isLoggedIn }">
            <i :class="isLoggedIn ? 'el-icon-check' : 'el-icon-close'"></i>
            <span>登录状态: {{ isLoggedIn ? '已登录' : '未登录' }}</span>
          </div>
        </div>
      </section>

      <!-- 详细信息 -->
      <section class="debug-section">
        <h2 class="section-title">详细信息</h2>
        <div class="detail-info">
          <div class="info-block">
            <h4>Token信息</h4>
            <div class="code-block">
              <pre>{{ tokenInfo }}</pre>
            </div>
          </div>
          
          <div class="info-block">
            <h4>用户信息</h4>
            <div class="code-block">
              <pre>{{ userInfoDisplay }}</pre>
            </div>
          </div>
        </div>
      </section>

      <!-- 操作按钮 -->
      <section class="debug-section">
        <h2 class="section-title">调试操作</h2>
        <div class="action-buttons">
          <el-button @click="refreshStatus" type="primary">刷新状态</el-button>
          <el-button @click="clearLoginInfo" type="danger">清除登录信息</el-button>
          <el-button @click="testTokenValidity" type="success" :loading="testing">测试Token有效性</el-button>
          <el-button @click="goToLogin" type="warning">前往登录</el-button>
        </div>
        
        <div v-if="testResult" class="test-result">
          <h4>Token测试结果:</h4>
          <div class="code-block">
            <pre>{{ JSON.stringify(testResult, null, 2) }}</pre>
          </div>
        </div>
      </section>

      <!-- 登录建议 -->
      <section class="debug-section">
        <h2 class="section-title">问题诊断和建议</h2>
        <div class="diagnosis">
          <div v-if="!hasToken && !hasUserInfo" class="issue error">
            <h4>❌ 完全未登录</h4>
            <p>系统中没有找到任何登录信息。</p>
            <p><strong>解决方案：</strong></p>
            <ol>
              <li>点击"前往登录"按钮</li>
              <li>输入正确的用户名和密码</li>
              <li>登录成功后返回此页面检查状态</li>
            </ol>
          </div>
          
          <div v-else-if="hasToken && !hasUserInfo" class="issue warning">
            <h4>⚠️ 部分登录信息缺失</h4>
            <p>有Token但缺少用户信息，可能是获取用户信息API失败。</p>
            <p><strong>解决方案：</strong>点击"测试Token有效性"检查Token是否有效</p>
          </div>
          
          <div v-else-if="!hasToken && hasUserInfo" class="issue warning">
            <h4>⚠️ Token缺失</h4>
            <p>有用户信息但缺少Token，这种情况很少见。</p>
            <p><strong>解决方案：</strong>清除登录信息后重新登录</p>
          </div>
          
          <div v-else-if="testResult && testResult.code !== 200 && testResult.code !== 1000" class="issue error">
            <h4>❌ Token无效</h4>
            <p>Token存在但已失效，无法验证用户身份。</p>
            <p><strong>解决方案：</strong>重新登录获取新的Token</p>
          </div>
          
          <div v-else class="issue success">
            <h4>✅ 登录状态正常</h4>
            <p>Token和用户信息都存在，登录状态正常。</p>
            <p>如果预约功能仍有问题，可能是后端处理逻辑问题。</p>
          </div>
        </div>
      </section>
    </div>
    
    <bottoms />
  </div>
</template>

<script>
import headers from '@/components/header'
import bottoms from '@/components/bottom'
import { getUser } from '@/api/api'

export default {
  name: 'LoginDebug',
  components: {
    headers,
    bottoms
  },
  data() {
    return {
      testing: false,
      testResult: null
    }
  },
  computed: {
    hasToken() {
      return !!localStorage.getItem('user_token');
    },
    hasUserInfo() {
      return !!localStorage.getItem('user_info');
    },
    isLoggedIn() {
      return this.hasToken && this.hasUserInfo;
    },
    tokenPreview() {
      const token = localStorage.getItem('user_token');
      if (!token) return '';
      return token.length > 50 ? token.substring(0, 50) + '...' : token;
    },
    tokenInfo() {
      const token = localStorage.getItem('user_token');
      if (!token) return '无Token';
      return `Token长度: ${token.length}\nToken内容: ${token}`;
    },
    userInfo() {
      const info = localStorage.getItem('user_info');
      if (!info) return {};
      try {
        return JSON.parse(info);
      } catch (e) {
        return {};
      }
    },
    userInfoDisplay() {
      const info = localStorage.getItem('user_info');
      if (!info) return '无用户信息';
      try {
        const parsed = JSON.parse(info);
        return JSON.stringify(parsed, null, 2);
      } catch (e) {
        return `解析错误: ${e.message}\n原始内容: ${info}`;
      }
    }
  },
  methods: {
    refreshStatus() {
      this.$forceUpdate();
      this.$message.info('状态已刷新');
    },
    
    clearLoginInfo() {
      localStorage.removeItem('user_token');
      localStorage.removeItem('user_info');
      this.testResult = null;
      this.$message.success('登录信息已清除');
      this.$forceUpdate();
    },
    
    async testTokenValidity() {
      if (!this.hasToken) {
        this.$message.error('没有Token，无法测试');
        return;
      }
      
      this.testing = true;
      this.testResult = null;
      
      try {
        console.log('🧪 测试Token有效性...');
        const result = await getUser();
        console.log('📥 Token测试结果:', result);
        
        this.testResult = result;
        
        if (result && (result.code === 200 || result.code === 1000)) {
          this.$message.success('Token有效，用户信息获取成功');
          
          // 如果用户信息获取成功但localStorage中没有，则保存
          if (result.data && !this.hasUserInfo) {
            localStorage.setItem('user_info', JSON.stringify(result.data));
            this.$message.info('已自动保存用户信息');
            this.$forceUpdate();
          }
        } else {
          this.$message.error('Token无效: ' + (result?.message || '未知错误'));
        }
      } catch (error) {
        console.error('❌ Token测试失败:', error);
        this.testResult = { error: error.message };
        this.$message.error('Token测试失败: ' + error.message);
      } finally {
        this.testing = false;
      }
    },
    
    goToLogin() {
      this.$router.push('/login');
    }
  },
  
  mounted() {
    console.log('🔑 登录状态调试工具已加载');
    console.log('Token存在:', this.hasToken);
    console.log('用户信息存在:', this.hasUserInfo);
    console.log('登录状态:', this.isLoggedIn);
  }
}
</script>

<style scoped lang="scss">
.login-debug-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.debug-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 40px 20px;
}

.page-title {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.debug-section {
  margin-bottom: 40px;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 12px;
  border-radius: 8px;
  
  &.success {
    background: #ecfdf5;
    color: #059669;
  }
  
  &.error {
    background: #fef2f2;
    color: #dc2626;
  }
  
  i {
    margin-right: 8px;
    font-size: 16px;
  }
  
  .token-preview, .user-preview {
    margin-left: auto;
    font-size: 12px;
    opacity: 0.8;
  }
}

.detail-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.info-block {
  h4 {
    margin: 0 0 8px 0;
    color: #374151;
  }
}

.code-block {
  background: #1f2937;
  color: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  
  pre {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-bottom: 20px;
  
  .el-button {
    border-radius: 8px;
  }
}

.test-result {
  h4 {
    margin: 0 0 8px 0;
    color: #374151;
  }
}

.diagnosis {
  .issue {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 12px;
    
    h4 {
      margin: 0 0 8px 0;
    }
    
    p, ol {
      margin: 8px 0;
    }
    
    &.error {
      background: #fef2f2;
      border-left: 4px solid #dc2626;
    }
    
    &.warning {
      background: #fffbeb;
      border-left: 4px solid #f59e0b;
    }
    
    &.success {
      background: #ecfdf5;
      border-left: 4px solid #059669;
    }
  }
}

@media (max-width: 768px) {
  .debug-content {
    padding: 20px 16px;
  }
  
  .debug-section {
    padding: 16px;
  }
  
  .action-buttons {
    .el-button {
      width: 100%;
      margin-bottom: 8px;
    }
  }
}
</style>
