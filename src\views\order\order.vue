<template>
  <div class="order-page">
    <headers />

    <div class="order-main-container"> 
      <el-card class="order-tabs-card" shadow="never">
        <el-tabs v-model="activeTabName" type="card" @tab-click="handleTabSwitch">
          <el-tab-pane label="景点订单" name="attraction">
            <div v-loading="loading.attraction" class="order-list-wrapper">
              <div class="card-grid-container" v-if="orders.attraction.list.length > 0">
                <div
                  class="order-card-item"
                  v-for="item in orders.attraction.list"
                  :key="item.id"
                  @click="openOrderDetailDialog(item, 'attraction')"
                >
                  <el-image class="card-item-image" :src="item.images && item.images.split(',')[0]" fit="cover" lazy>
                     <div slot="placeholder" class="image-slot-placeholder"><i class="el-icon-loading"></i></div>
                     <div slot="error" class="image-slot-error"><i class="el-icon-picture-outline"></i></div>
                  </el-image>
                  <div class="card-item-content">
                    <h3 class="card-item-title" :title="item.name">{{ item.name | truncate(20) }}</h3>
                    <p class="card-item-desc" :title="item.introduce">{{ item.introduce | truncate(30) }}</p>
                    <el-tag :type="getOrderStatusTagType(item.state)" size="mini" class="order-status-tag">{{ getOrderStatusText(item.state) }}</el-tag>
                  </div>
                </div>
              </div>
              <el-empty v-else description="暂无景点订单" />
              <el-pagination
                v-if="orders.attraction.total > 0"
                class="pagination-center"
                background
                :current-page.sync="orders.attraction.params.pageNumber"
                :page-size="orders.attraction.params.pageSize"
                layout="total, prev, pager, next, jumper"
                :total="orders.attraction.total"
                @current-change="page => handlePageChange('attraction', page)"
              />
            </div>
          </el-tab-pane>

          <el-tab-pane label="酒店订单" name="hotel">
             <div v-loading="loading.hotel" class="order-list-wrapper">
              <div class="card-grid-container" v-if="orders.hotel.list.length > 0">
                <div
                  class="order-card-item"
                  v-for="item in orders.hotel.list"
                  :key="item.id"
                  @click="openOrderDetailDialog(item, 'hotel')"
                >
                  <el-image class="card-item-image" :src="item.images && item.images.split(',')[0]" fit="cover" lazy>
                     <div slot="placeholder" class="image-slot-placeholder"><i class="el-icon-loading"></i></div>
                     <div slot="error" class="image-slot-error"><i class="el-icon-picture-outline"></i></div>
                  </el-image>
                  <div class="card-item-content">
                    <h3 class="card-item-title" :title="item.name">{{ item.name | truncate(20) }}</h3>
                    <p class="card-item-desc" :title="item.introduce">{{ item.introduce | truncate(30) }}</p>
                     <el-tag :type="getOrderStatusTagType(item.state)" size="mini" class="order-status-tag">{{ getOrderStatusText(item.state) }}</el-tag>
                  </div>
                </div>
              </div>
              <el-empty v-else description="暂无酒店订单" />
              <el-pagination
                v-if="orders.hotel.total > 0"
                class="pagination-center"
                background
                :current-page.sync="orders.hotel.params.pageNumber"
                :page-size="orders.hotel.params.pageSize"
                layout="total, prev, pager, next, jumper"
                :total="orders.hotel.total"
                @current-change="page => handlePageChange('hotel', page)"
              />
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
    <bottoms />

    <el-dialog
      :title="orderDetailDialog.title"
      :visible.sync="orderDetailDialog.visible"
      width="550px"
      :before-close="closeOrderDetailDialog"
      class="order-detail-dialog"
      append-to-body
      destroy-on-close 
    >
      <div v-if="orderDetailDialog.currentOrder" class="dialog-content-wrapper">
        <el-descriptions direction="vertical" :column="2" border size="small" class="order-descriptions">
          <el-descriptions-item label="订单状态">
            <el-tag :type="getOrderStatusTagType(orderDetailDialog.currentOrder.state)" size="small">
              {{ getOrderStatusText(orderDetailDialog.currentOrder.state) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item :label="orderDetailDialog.type === 'attraction' ? '预订数量' : '间/夜数'">
            {{ orderDetailDialog.currentOrder.num }}
          </el-descriptions-item>
          <el-descriptions-item :label="orderDetailDialog.type === 'attraction' ? '预约日期' : '入住日期'" :span="2">
            {{ orderDetailDialog.currentOrder.time | formatDate }}
          </el-descriptions-item>
        </el-descriptions>
        
        <div class="traveler-info-section" v-if="parsedTravelers.length > 0">
          <h4>{{ orderDetailDialog.type === 'attraction' ? '游客信息' : '入住人信息' }}</h4>
          <el-table :data="parsedTravelers" stripe size="mini" class="traveler-table" border>
            <el-table-column prop="name" label="姓名" min-width="80"></el-table-column>
            <el-table-column prop="tel" label="电话" min-width="110"></el-table-column>
            <el-table-column prop="idCard" label="身份证号" min-width="150"></el-table-column>
          </el-table>
        </div>
         <div v-else-if="orderDetailDialog.currentOrder.people && typeof orderDetailDialog.currentOrder.people === 'string'" class="traveler-info-section">
            <el-alert title="游客/入住人信息解析失败" type="warning" :closable="false" show-icon description="请检查订单中的出行人信息格式是否正确。"></el-alert>
        </div>
      </div>
      <div v-else class="dialog-loading">
          <i class="el-icon-loading"></i> 订单详情加载中...
      </div>

      <template #footer>
        <el-button size="small" @click="closeOrderDetailDialog">关 闭</el-button>
        <el-button
          size="small"
          type="danger"
          plain
          v-if="orderDetailDialog.currentOrder && canCancelOrder(orderDetailDialog.currentOrder.state)"
          @click="handleCancelOrder"
          :loading="orderDetailDialog.canceling"
        >
          申请取消订单
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getSysHotelOrderPage, 
  getSysAttractionOrderPage, 
  editSysAttractionOrder, 
  editSysHotelOrder 
} from '../../api/api';
import headers from '@/components/header';
import bottoms from '@/components/bottom';

// 辅助函数：日期格式化
const formatDate = (value, format = 'YYYY-MM-DD') => {
  if (!value) return '';
  const date = new Date(value);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  // 可根据 format 参数扩展更多格式
  return `${year}-${month}-${day}`;
};

// 订单状态映射
const ORDER_STATUS_MAP = {
  0: { text: '待确认', type: 'info' },
  1: { text: '已确认', type: 'success' },
  2: { text: '取消中', type: 'warning' },
  3: { text: '已取消', type: 'danger' },
  4: { text: '取消失败', type: 'warning' },
  5: { text: '已使用', type: 'primary' }, // 'primary' 或其他，如置灰 'info'
  default: { text: '未知状态', type: 'info' }
};

export default {
  name: 'OrderListPage',
  components: { headers, bottoms },
  filters: {
    truncate(text, length, suffix = '...') {
      if (typeof text !== 'string') return '';
      return text.length > length ? text.substring(0, length) + suffix : text;
    },
    formatDate(value) { return formatDate(value); }
  },
  data() {
    return {
      activeTabName: 'attraction', // 默认激活的Tab, 使用更有意义的名称
      orders: { // 按类型组织订单数据和分页参数
        attraction: {
          params: { userId: '', pageSize: 8, pageNumber: 1 }, // 每页8条，调整数量
          list: [],
          total: 0,
        },
        hotel: {
          params: { userId: '', pageSize: 8, pageNumber: 1 },
          list: [],
          total: 0,
        },
      },
      loading: { // 按类型组织加载状态
        attraction: false,
        hotel: false,
      },
      orderDetailDialog: { // 复用的对话框状态
        visible: false,
        title: '订单详情',
        type: '', // 'attraction' or 'hotel'
        currentOrder: null,
        canceling: false,
      },
      userId: null,
    };
  },
  computed: {
    parsedTravelers() {
      const peopleJson = this.orderDetailDialog.currentOrder?.people;
      if (peopleJson) {
        try {
          const peopleData = JSON.parse(peopleJson);
          return Array.isArray(peopleData) ? peopleData : [peopleData];
        } catch (e) {
          console.warn("解析游客/入住人信息失败:", peopleJson, e);
          return [];
        }
      }
      return [];
    }
  },
  methods: {
    // --- 初始化与数据加载 ---
    async initializePageData() {
      const userInfoString = window.localStorage.getItem("user_info");
      if (userInfoString) {
        try {
          const userInfo = JSON.parse(userInfoString);
          if (userInfo && userInfo.id) {
            this.userId = userInfo.id;
            this.orders.attraction.params.userId = userInfo.id;
            this.orders.hotel.params.userId = userInfo.id;
            this.loadDataForCurrentTab(); // 加载当前激活Tab的数据
            return;
          }
        } catch (e) { console.error("解析用户信息失败:", e); }
      }
      this.$message.warning('请先登录以查看您的订单。');
      // 可选： this.$router.push('/login');
    },
    async loadDataForCurrentTab() {
      if (this.activeTabName === 'attraction') {
        await this.fetchAttractionOrders();
      } else if (this.activeTabName === 'hotel') {
        await this.fetchHotelOrders();
      }
    },
    async fetchAttractionOrders() {
      if (!this.userId) return;
      this.loading.attraction = true;
      try {
        const res = await getSysAttractionOrderPage(this.orders.attraction.params);
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.orders.attraction.list = res.data.records || [];
          this.orders.attraction.total = Number(res.data.total) || 0;
        } else {
          this.$message.error(res.message || '获取景点订单失败');
        }
      } catch (error) { 
        console.error('获取景点订单异常:', error); 
        this.$message.error('网络错误，景点订单加载失败');
      } finally { 
        this.loading.attraction = false; 
      }
    },
    async fetchHotelOrders() {
      if (!this.userId) return;
      this.loading.hotel = true;
      try {
        const res = await getSysHotelOrderPage(this.orders.hotel.params);
        if (res && (res.code === 1000 || res.code === 200) && res.data) {
          this.orders.hotel.list = res.data.records || [];
          this.orders.hotel.total = Number(res.data.total) || 0;
        } else {
          this.$message.error(res.message || '获取酒店订单失败');
        }
      } catch (error) { 
        console.error('获取酒店订单异常:', error); 
        this.$message.error('网络错误，酒店订单加载失败');
      } finally { 
        this.loading.hotel = false; 
      }
    },

    // --- 事件处理器 ---
    handleTabSwitch(tab) { // el-tabs的 @tab-click 事件
      this.activeTabName = tab.name; // 更新当前激活的tab
      // 仅当对应列表为空时（或者您希望每次切换都刷新）加载数据
      if (this.activeTabName === 'attraction' && this.orders.attraction.list.length === 0) {
         this.orders.attraction.params.pageNumber = 1;
         this.fetchAttractionOrders();
      } else if (this.activeTabName === 'hotel' && this.orders.hotel.list.length === 0) {
         this.orders.hotel.params.pageNumber = 1;
         this.fetchHotelOrders();
      }
    },
    handlePageChange(page, type) {
      this.orders[type].params.pageNumber = page;
      if (type === 'attraction') {
        this.fetchAttractionOrders();
      } else if (type === 'hotel') {
        this.fetchHotelOrders();
      }
    },

    // --- 对话框和订单操作 ---
    openOrderDetailDialog(order, type) {
      this.orderDetailDialog.currentOrder = { ...order };
      this.orderDetailDialog.type = type;
      this.orderDetailDialog.title = `${type === 'attraction' ? '景点' : '酒店'}订单详情 - ${order.name || ''}`;
      this.orderDetailDialog.visible = true;
    },
    closeOrderDetailDialog() {
      this.orderDetailDialog.visible = false;
      // currentOrder 会在 destroy-on-close 时被Vue销毁对话框内容时清理，
      // 但手动清理一下更好，以防万一或用于非destroy-on-close场景
      this.orderDetailDialog.currentOrder = null; 
      this.orderDetailDialog.canceling = false;
    },
    getOrderStatusText(state) {
      return (ORDER_STATUS_MAP[state] || ORDER_STATUS_MAP.default).text;
    },
    getOrderStatusTagType(state) {
      return (ORDER_STATUS_MAP[state] || ORDER_STATUS_MAP.default).type;
    },
    canCancelOrder(state) {
      return state === 0 || state === 1; // 0:未确认, 1:已确认
    },
    async handleCancelOrder() {
      const order = this.orderDetailDialog.currentOrder;
      if (!order || !order.id) return;

      try {
        await this.$confirm(
          `确定要申请取消订单 “${order.name}” 吗？部分产品取消可能产生费用。`, 
          '取消订单确认', 
          { confirmButtonText: '确定申请', cancelButtonText: '再想想', type: 'warning' }
        );
        
        this.orderDetailDialog.canceling = true;
        const params = { id: order.id, state: 2 }; // 2:取消中
        let success = false;

        if (this.orderDetailDialog.type === 'attraction') {
          const res = await editSysAttractionOrder(params);
          success = res && (res.code === 1000 || res.code === 200);
          if (!success) this.$message.error(res.message || '景点订单取消申请失败');
        } else if (this.orderDetailDialog.type === 'hotel') {
          const res = await editSysHotelOrder(params);
          success = res && (res.code === 1000 || res.code === 200);
          if (!success) this.$message.error(res.message || '酒店订单取消申请失败');
        }

        if (success) {
          this.$message.success('取消申请已提交，请等待审核。');
          this.closeOrderDetailDialog();
          this.loadDataForCurrentTab(); // 刷新当前Tab列表
        }
      } catch (error) {
        if (error !== 'cancel') { 
             console.error('取消订单操作异常:', error);
             this.$message.error('操作失败，请稍后重试');
        }
      } finally {
        this.orderDetailDialog.canceling = false;
      }
    }
  },
  created() {
    this.initializePageData();
  },
};
</script>

<style scoped lang="scss">
// --- SCSS Variables ---
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

$text-color-primary: #303133;
$text-color-regular: #606266;
$text-color-secondary: #909399;
$border-color-base: #DCDFE6;
$border-color-light: #E4E7ED;
$border-color-lighter: #ebeef5;
$bg-color-page: #f5f7fa;
$card-shadow: 0 2px 6px rgba(0,0,0,.04);
$card-hover-shadow: 0 4px 10px rgba(0,0,0,.08);
$card-border-radius: 6px;

// --- Page & Content Wrapper ---
.order-page {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', /* ... */;
  background-color: $bg-color-page;
  min-height: calc(100vh - 120px); 
  display: flex;
  flex-direction: column;
}

.order-main-container {
  width: 85%; // 调整内容区宽度
  max-width: 1200px; // 最大宽度
  margin: 20px auto;
  flex-grow: 1;
}

// --- Tabs Card ---
.order-tabs-card {
  border-radius: $card-border-radius;
  box-shadow: $card-shadow;
  ::v-deep .el-tabs__header {
    margin-bottom: 0; 
    border-top-left-radius: $card-border-radius;
    border-top-right-radius: $card-border-radius;
  }
  ::v-deep .el-tabs__nav { // 确保nav区域也适配圆角
    border-top-left-radius: $card-border-radius;
    border-top-right-radius: $card-border-radius;
    border-bottom: 1px solid $border-color-light; // header和content之间的分割线
     .el-tabs__item {
        font-size: 14px;
        height: 45px; // tab页签高度
        line-height: 45px;
        &:hover {
            color: $primary-color;
        }
        &.is-active {
            color: $primary-color;
            font-weight: 500;
        }
    }
  }
  ::v-deep .el-tabs__content {
    padding: 15px 20px; // Tab内容区内边距
  }
}

// --- Order List ---
.order-list-wrapper {
  min-height: 350px; 
}

.card-grid-container { // 代替之前的 .order-list
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); // 卡片最小宽度调整
  gap: 18px; // 卡片间距调整
}

.order-card-item { // 自定义卡片样式
  background: #fff;
  border-radius: $card-border-radius - 2px;
  border: 1px solid $border-color-lighter;
  box-shadow: $card-shadow;
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  overflow: hidden; // 确保图片圆角

  &:hover {
    transform: translateY(-3px);
    box-shadow: $card-hover-shadow;
  }

  .order-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    border-bottom: 1px solid $border-color-lighter;

    .order-name {
      font-size: 13px; // 标题略小
      font-weight: 500;
      color: $text-color-primary;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      margin-right: 8px;
    }
    .order-status-tag { // 订单状态标签
        // el-tag 默认样式即可，或者微调
    }
  }

  .card-item-image { // el-image
    width: 100%;
    height: 150px; // 图片高度调整
    display: block;
    background-color: #f0f2f5; // 图片加载时背景
  }
  .image-slot-placeholder, .image-slot-error { // el-image 占位符
    display: flex; flex-direction: column; justify-content: center; align-items: center;
    width: 100%; height: 100%; background: #f5f7fa; color: $text-color-secondary;
    font-size: 13px; text-align: center;
    i { font-size: 24px; margin-bottom: 6px; }
  }
  .image-slot-placeholder i { animation: rotating 2s linear infinite; }
  @keyframes rotating { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }


  .card-item-content {
    padding: 10px 12px; // 内容区内边距
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    font-size: 12px; // 内容区文字略小
    color: $text-color-regular;
    line-height: 1.6;

    .card-item-title { // 这是订单名称，已移到header，这里可以放其他信息
        // font-size: 14px; font-weight: 500; color: $text-color-primary; margin-bottom: 5px;
    }
    .card-item-desc { // 订单简介
        margin: 0 0 8px 0;
        // CSS多行截断
        overflow: hidden; text-overflow: ellipsis; display: -webkit-box;
        -webkit-line-clamp: 2; -webkit-box-orient: vertical;
        min-height: calc(1.6em * 2); // 预留2行高度
    }
  }
   .order-item-summary { // 替换 card-item-desc，用于更结构化的信息
    padding: 10px 12px;
    font-size: 12px;
    color: $text-color-regular;
    line-height: 1.7;
    p {
      margin: 0 0 4px 0;
      display: flex;
      align-items: center;
      i { margin-right: 5px; color: $text-color-secondary; font-size: 13px;}
    }
  }
}

// --- Empty State & Pagination ---
.order-empty { padding: 40px 0; .el-button { margin-top: 15px; } }
.pagination-center { margin-top: 25px; text-align: center; padding-bottom: 10px; }


// --- Order Detail Dialog ---
.order-detail-dialog {
  ::v-deep .el-dialog__header {
    padding: 15px 20px; // 对话框头部内边距
    border-bottom: 1px solid $border-color-light;
    .el-dialog__title { font-size: 16px; font-weight: 500; } // 标题样式
  }
  ::v-deep .el-dialog__body {
    padding: 15px 20px 20px 20px; 
  }
  .dialog-content-wrapper { font-size: 13px; }
  .dialog-loading { /* ... */ }

  .el-descriptions {
    margin-bottom: 15px;
    ::v-deep .el-descriptions-item__label {
      font-weight: normal; // 标签字重
      color: $text-color-regular;
      width: 90px; // 统一标签宽度
      font-size: 13px;
    }
     ::v-deep .el-descriptions-item__content {
      color: $text-color-primary;
      font-size: 13px;
    }
  }

  .traveler-info-section {
    margin-top: 18px;
    h4 {
      font-size: 13px; font-weight: 500; color: $text-color-primary;
      margin-bottom: 8px; padding-bottom: 6px;
      border-bottom: 1px dashed $border-color-lighter;
    }
    .traveler-table {
      width: 100%; font-size: 12px;
      ::v-deep th { background-color: #fafcff; font-weight: 500; color: $text-color-regular; padding: 6px 0;}
      ::v-deep td { padding: 6px 0; }
    }
    .el-alert { margin-top: 10px; }
  }
}
</style>