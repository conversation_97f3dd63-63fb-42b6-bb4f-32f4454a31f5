<template>
  <div class="register-page-wrapper">
    <div class="register-background">
    </div>
    <div class="register-form-container">
      <el-card class="register-card" shadow="lg">
        <div class="register-header">
          <!-- <img src="../../assets/image/image 2.png" alt="Logo" class="register-logo" /> -->
          <h2 class="register-title">欢迎注册旅游推荐平台</h2>
        </div>

        <el-form :model="registerForm" :rules="registerRules" ref="registerFormRef" @submit.native.prevent="handleRegister">
          <el-form-item prop="loginAccount">
            <el-input
              v-model="registerForm.loginAccount"
              placeholder="请输入登录账号 (例如：手机号或邮箱)"
              prefix-icon="el-icon-user"
              clearable
            />
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              type="password"
              v-model="registerForm.password"
              placeholder="请输入密码 (至少6位)"
              prefix-icon="el-icon-lock"
              show-password
              autocomplete="new-password"
            />
          </el-form-item>
          <el-form-item prop="confirmPassword"> 
            <el-input
              type="password"
              v-model="registerForm.confirmPassword"
              placeholder="请再次输入密码"
              prefix-icon="el-icon-check"
              show-password
              autocomplete="new-password"
            />
          </el-form-item>
          <el-form-item prop="userName">
            <el-input
              v-model="registerForm.userName"
              placeholder="请输入用户名/昵称"
              prefix-icon="el-icon-s-custom" 
              clearable
            />
          </el-form-item>
          <el-form-item prop="email">
            <el-input
              v-model="registerForm.email"
              placeholder="请输入邮箱 (用于接收通知或找回密码)"
              prefix-icon="el-icon-message"
              clearable
            />
          </el-form-item>
          <el-form-item prop="tel">
            <el-input
              v-model="registerForm.tel"
              placeholder="请输入联系电话"
              prefix-icon="el-icon-phone-outline"
              clearable
            />
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              class="register-button-submit"
              @click="handleRegister"
              :loading="isLoading"
            >
              立即注册
            </el-button>
          </el-form-item>
          <el-form-item class="login-link-item">
            <el-button type="text" @click="navigateToLogin" class="login-link">
              已有账号？直接登录
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import { saveUser } from '../../api/api'; // 确认API路径

export default {
  name: 'RegisterPage', // 1. 组件命名
  data() {
    // 校验密码一致性
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.registerForm.password) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    };
    // 邮箱校验
    const validateEmail = (rule, value, callback) => {
        if (!value) {
            return callback(new Error('请输入邮箱'));
        }
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        if (!emailRegex.test(value)) {
            return callback(new Error('请输入正确的邮箱格式'));
        }
        callback();
    };
    // 手机号校验
    const validatePhoneNumber = (rule, value, callback) => {
        if (!value) {
            return callback(new Error('请输入联系电话'));
        }
        const phoneRegex = /^1[3-9]\d{9}$/;
        if (!phoneRegex.test(value)) {
            return callback(new Error('请输入正确的11位手机号码'));
        }
        callback();
    };


    return {
      registerForm: { // 2. 表单数据对象
        loginAccount: '',
        password: '',
        confirmPassword: '', // 增加确认密码字段
        userName: '',
        email: '',
        tel: '',
        userType: 1, // 假设用户类型固定为1 (普通用户)
      },
      registerRules: { // 3. 表单校验规则
        loginAccount: [
          { required: true, message: '请输入登录账号', trigger: 'blur' },
          { min: 3, message: '登录账号长度至少为3位', trigger: 'blur' },
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度应为 6 到 20 个字符', trigger: 'blur' },
        ],
        confirmPassword: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' },
        ],
        userName: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { validator: validateEmail, trigger: ['blur', 'change'] },
        ],
        tel: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { validator: validatePhoneNumber, trigger: 'blur'}
        ],
      },
      isLoading: false, // 4. 加载状态
    };
  },
  methods: {
    navigateToLogin() {
      this.$router.push('/login');
    },
    handleRegister() { // 5. 注册方法优化
      this.$refs.registerFormRef.validate(async (valid) => {
        if (valid) {
          this.isLoading = true;
          try {
            // 从表单数据中提取需要提交给后端的参数
            const paramsToSubmit = {
              loginAccount: this.registerForm.loginAccount,
              password: this.registerForm.password,
              userName: this.registerForm.userName,
              email: this.registerForm.email,
              tel: this.registerForm.tel,
              userType: this.registerForm.userType, // 或者直接在后端设置默认值
            };

            const res = await saveUser(paramsToSubmit);

            if (res && (res.code === 1000 || res.code === 200)) {
              // 根据您原代码中的 type 判断逻辑，这里假设 userType 影响提示信息
              // 如果 this.registerForm.userType === 2 (假设是需要审核的类型)
              // if (paramsToSubmit.userType === 2) { 
              //   this.$message.success('注册申请已提交，审核完成后会将结果发送到您的邮箱，请注意查收');
              // } else {
              //   this.$message.success('注册成功！');
              // }
              this.$message.success('注册成功！请前往登录。'); // 统一提示

              setTimeout(() => {
                this.$router.push('/login');
              }, 1000); // 延迟跳转，给用户看提示的时间

            } else {
              this.$message.error(res.message || '注册失败，请稍后重试');
            }
          } catch (error) {
            console.error('注册API请求异常:', error);
            if (error.response && error.response.data && error.response.data.message) {
                 this.$message.error(`注册失败: ${error.response.data.message}`);
            } else {
                 this.$message.error('注册请求失败，请检查网络或联系管理员');
            }
          } finally {
            this.isLoading = false;
          }
        } else {
          console.log('注册表单校验失败');
          this.$message.warning('请检查表单信息是否填写正确。');
          return false;
        }
      });
    },
  },
  created() {
    // 可以在此检查是否已登录，如果已登录则重定向到首页，避免重复注册
    if (window.localStorage.getItem("user_token")) {
      // this.$router.replace('/'); 
    }
  },
};
</script>

<style scoped lang="scss">
// --- SCSS Variables (与 login.vue 保持一致或全局定义) ---
$primary-color: #409EFF;
$brand-color: #d37136; 
$text-color-primary: #303133;
$text-color-on-dark-bg: #ffffff;
$login-card-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
$input-height: 40px;
$border-color-light: #dcdfe6;
$text-color-secondary: #909399; // <--- `$text-color-secondary` 在这里定义

// --- Page Wrapper (与 login.vue 样式类似) ---
.register-page-wrapper {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  // background: url('../../assets/image/register-background.jpg') no-repeat center center / cover; 
  background-image: linear-gradient(135deg, #fbc2eb 0%, #a6c1ee 100%); // 示例渐变
}

// --- Register Form ---
.register-form-container {
  //
}

.register-card {
  width: 400px; // 注册卡片可以比登录卡片略宽一点，因为字段更多
  padding: 20px 25px 25px;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.92); // 半透明背景
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  box-shadow: $login-card-shadow;

  .register-header {
    text-align: center;
    margin-bottom: 20px; // 头部与表单间距减小

    .register-logo {
      width: 40px; // Logo尺寸
      height: auto;
      vertical-align: middle;
      margin-right: 8px;
    }

    .register-title {
      font-size: 18px; // 标题字号
      color: $text-color-primary; // 调整颜色以适应浅色透明背景
      font-weight: 500;
      display: inline-block;
      vertical-align: middle;
    }
  }

  .el-form-item {
    margin-bottom: 18px; // 表单项间距

    ::v-deep .el-input__inner {
      height: $input-height;
      line-height: $input-height;
      border-radius: 4px;
      &:focus {
        border-color: $primary-color;
      }
    }
    ::v-deep .el-input__prefix {
        display: flex;
        align-items: center;
        height: 100%;
     }
  }
  
  .register-button-submit { // 注册按钮
    width: 100%;
    height: $input-height;
    font-size: 15px;
    border-radius: 4px;
    letter-spacing: 1px;
  }
  .login-link-item { // “已有账号？直接登录”
      margin-top: 15px;
      margin-bottom: 0; // 最后一个item通常不需要下边距
      text-align: center;
      .login-link {
          font-size: 13px;
          color: $text-color-secondary;
           &:hover {
            color: $primary-color;
           }
      }
  }
}

// 确保您在 login.css 或全局样式中没有与这些scoped样式冲突的规则
// 如果 login.css 中有 .login1 (背景), .login4, .login5 (表单容器) 的样式，
// 需要将它们与这里的 .register-page-wrapper, .register-form-container, .register-card 的样式协调。
</style>