<template>
  <div class="modern-attractions">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <i class="el-icon-place"></i>
            景点管理
          </h1>
          <p class="page-description">管理系统中的所有景点信息，包括新增、编辑、删除和状态管理</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" icon="el-icon-plus" class="modern-btn" @click="add">
            新增景点
          </el-button>
          <el-button icon="el-icon-refresh" class="modern-btn" @click="refresh">
            刷新数据
          </el-button>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <div class="search-card">
        <div class="search-header">
          <h3>
            <i class="el-icon-search"></i>
            搜索筛选
          </h3>
          <el-button
            type="text"
            icon="el-icon-arrow-up"
            class="collapse-btn"
            :class="{ collapsed: searchCollapsed }"
            @click="searchCollapsed = !searchCollapsed"
          >
            {{ searchCollapsed ? '展开' : '收起' }}
          </el-button>
        </div>

        <el-collapse-transition>
          <div v-show="!searchCollapsed" class="search-content">
            <el-form :model="search" class="search-form" :inline="true">
              <el-form-item label="景点名称">
                <el-input
                  v-model="search.name"
                  placeholder="请输入景点名称"
                  class="search-input"
                  clearable
                  @keyup.enter.native="searchPage"
                >
                  <i slot="prefix" class="el-icon-place"></i>
                </el-input>
              </el-form-item>

              <el-form-item label="状态">
                <el-select v-model="search.state" placeholder="请选择状态" class="search-select" clearable>
                  <el-option label="全部" value=""></el-option>
                  <el-option label="上架" value="1"></el-option>
                  <el-option label="下架" value="0"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="价格范围">
                <el-input-number
                  v-model="search.minPrice"
                  placeholder="最低价格"
                  :min="0"
                  class="price-input"
                ></el-input-number>
                <span class="price-separator">-</span>
                <el-input-number
                  v-model="search.maxPrice"
                  placeholder="最高价格"
                  :min="0"
                  class="price-input"
                ></el-input-number>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="searchPage" class="search-btn">
                  搜索
                </el-button>
                <el-button icon="el-icon-refresh" @click="resetSearch" class="reset-btn">
                  重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-collapse-transition>
      </div>
    </div>

    <!-- 数据统计卡片 -->
    <div class="stats-section">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon total">
            <i class="el-icon-s-data"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ total }}</div>
            <div class="stat-label">总景点数</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon active">
            <i class="el-icon-success"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ activeCount }}</div>
            <div class="stat-label">上架景点</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon inactive">
            <i class="el-icon-warning"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ inactiveCount }}</div>
            <div class="stat-label">下架景点</div>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon selected">
            <i class="el-icon-check"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ selectedCount }}</div>
            <div class="stat-label">已选择</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-section">
      <div class="table-card">
        <!-- 表格工具栏 -->
        <div class="table-toolbar">
          <div class="toolbar-left">
            <el-button
              type="primary"
              icon="el-icon-plus"
              class="toolbar-btn"
              @click="add"
            >
              新增
            </el-button>
            <el-button
              type="success"
              icon="el-icon-edit"
              class="toolbar-btn"
              :disabled="selectedRows.length !== 1"
              @click="updateDataBtn"
            >
              修改
            </el-button>
            <el-button
              type="danger"
              icon="el-icon-delete"
              class="toolbar-btn"
              :disabled="selectedRows.length === 0"
              @click="deleteDataBtn"
            >
              删除 ({{ selectedRows.length }})
            </el-button>
          </div>

          <div class="toolbar-right">
            <el-tooltip content="刷新数据" placement="top">
              <el-button icon="el-icon-refresh" circle class="icon-btn" @click="refresh"></el-button>
            </el-tooltip>
            <el-tooltip content="导出数据" placement="top">
              <el-button icon="el-icon-download" circle class="icon-btn" @click="exportData"></el-button>
            </el-tooltip>
            <el-tooltip content="列设置" placement="top">
              <el-button icon="el-icon-setting" circle class="icon-btn" @click="showColumnSettings"></el-button>
            </el-tooltip>
          </div>
        </div>

        <!-- 现代化表格 -->
        <div class="table-container">
          <el-table
            v-loading="loading"
            :data="tableData"
            @selection-change="handleSelectionChange"
            class="modern-table"
            :header-cell-style="{ background: 'var(--glass-bg)', color: 'var(--text-primary)' }"
            stripe
            border
          >
            <el-table-column type="selection" width="55" align="center"></el-table-column>

            <el-table-column prop="name" label="景点名称" min-width="150" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="name-cell">
                  <el-avatar :size="40" :src="scope.row.image" class="attraction-avatar">
                    <i class="el-icon-place"></i>
                  </el-avatar>
                  <div class="name-info">
                    <div class="name-text">{{ scope.row.name }}</div>
                    <div class="name-subtitle">ID: {{ scope.row.id }}</div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="price" label="价格" width="120" align="center">
              <template slot-scope="scope">
                <div class="price-cell">
                  <span class="price-symbol">¥</span>
                  <span class="price-value">{{ scope.row.price }}</span>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="introduce" label="简介" min-width="200" show-overflow-tooltip>
              <template slot-scope="scope">
                <div class="intro-cell">{{ scope.row.introduce }}</div>
              </template>
            </el-table-column>

            <el-table-column prop="num" label="库存" width="100" align="center">
              <template slot-scope="scope">
                <el-tag
                  :type="scope.row.num > 50 ? 'success' : scope.row.num > 10 ? 'warning' : 'danger'"
                  class="stock-tag"
                >
                  {{ scope.row.num }}
                </el-tag>
              </template>
            </el-table-column>

            <el-table-column prop="state" label="状态" width="100" align="center">
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.state"
                  :active-value="1"
                  :inactive-value="0"
                  active-color="var(--primary-color)"
                  inactive-color="#dcdfe6"
                  @change="toggleState(scope.row)"
                >
                </el-switch>
              </template>
            </el-table-column>

            <el-table-column prop="createTime" label="创建时间" width="180" align="center">
              <template slot-scope="scope">
                <div class="time-cell">
                  <i class="el-icon-time"></i>
                  {{ formatTime(scope.row.createTime) }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="280" align="center" fixed="right">
              <template slot-scope="scope">
                <div class="action-buttons">
                  <el-tooltip content="查看评论" placement="top">
                    <el-button
                      type="primary"
                      icon="el-icon-chat-dot-round"
                      circle
                      size="mini"
                      class="action-btn"
                      @click="toComment(scope.row.id)"
                    ></el-button>
                  </el-tooltip>

                  <el-tooltip content="编辑" placement="top">
                    <el-button
                      type="success"
                      icon="el-icon-edit"
                      circle
                      size="mini"
                      class="action-btn"
                      @click="updateData(scope.row.id)"
                    ></el-button>
                  </el-tooltip>

                  <el-tooltip content="删除" placement="top">
                    <el-popconfirm
                      title="确认删除这个景点吗？"
                      confirm-button-text="确认"
                      cancel-button-text="取消"
                      icon="el-icon-info"
                      icon-color="red"
                      @confirm="deleteDate(scope.row.id)"
                    >
                      <el-button
                        slot="reference"
                        type="danger"
                        icon="el-icon-delete"
                        circle
                        size="mini"
                        class="action-btn"
                      ></el-button>
                    </el-popconfirm>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <!-- 现代化分页 -->
        <div class="pagination-container">
          <el-pagination
            background
            layout="total, sizes, prev, pager, next, jumper"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="search.pageSize"
            :current-page="search.pageNumber"
            :total="total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            class="modern-pagination"
          >
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 对话框组件 -->
    <add @addFalse="addFalse" :addVisible="addVisible"></add>
    <update @updateFalse="updateFalse" :updateId="updateId" :updateVisible="updateVisible"></update>
  </div>
</template>

<script>
  import add from '../../../components/system/attractions/addAttractions'
  import update from '../../../components/system/attractions/updateAttractions'
  import { getSysAttractionsPage, removeSysAttractions, editSysAttractions } from '../../../api/api'

  export default {
    name: "ModernAttractions",
    data() {
      return {
        loading: true,
        selectedRows: [],
        updateId: "",
        addVisible: false,
        updateVisible: false,
        searchCollapsed: false,
        search: {
          name: "",
          state: "",
          minPrice: null,
          maxPrice: null,
          pageNumber: 1,
          pageSize: 10
        },
        total: 0,
        tableData: []
      }
    },
    components: {
      add,
      update
    },
    computed: {
      // 计算统计数据
      activeCount() {
        return this.tableData.filter(item => item.state === 1).length;
      },
      inactiveCount() {
        return this.tableData.filter(item => item.state === 0).length;
      },
      selectedCount() {
        return this.selectedRows.length;
      }
    },
    methods: {
      // 切换景点状态
      toggleState(row) {
        const param = {
          id: row.id,
          state: row.state
        };

        editSysAttractions(param).then(res => {
          if (res.code === 1000) {
            this.$message.success(`景点已${row.state ? '上架' : '下架'}`);
            this.query();
          } else {
            // 如果失败，恢复原状态
            row.state = row.state === 1 ? 0 : 1;
            this.$message.error(res.message || '操作失败');
          }
        }).catch(() => {
          // 如果失败，恢复原状态
          row.state = row.state === 1 ? 0 : 1;
          this.$message.error('网络错误，请稍后重试');
        });
      },

      // 跳转到评论页面
      toComment(id) {
        const param = {
          name: "景点评论",
          url: "/comments?id=" + id
        };
        this.$store.commit('menu/addActiveMenu', param);
        this.$router.push("/comments?id=" + id);
        this.$store.commit('menu/setActiveMenu', "/comments?id=" + id);
      },

      // 搜索
      searchPage() {
        this.search.pageNumber = 1;
        this.query();
      },

      // 重置搜索
      resetSearch() {
        this.search = {
          name: "",
          state: "",
          minPrice: null,
          maxPrice: null,
          pageNumber: 1,
          pageSize: this.search.pageSize
        };
        this.query();
      },

      // 查询数据
      query() {
        this.loading = true;

        // 构建查询参数
        const params = { ...this.search };

        getSysAttractionsPage(params).then(res => {
          if (res.code === 1000) {
            this.tableData = res.data.records || [];
            this.total = res.data.total || 0;
          } else {
            this.$notify.error({
              title: '错误',
              message: res.message || '获取数据失败'
            });
          }
        }).catch(error => {
          console.error('查询错误:', error);
          this.$notify.error({
            title: '错误',
            message: '网络错误，请稍后重试'
          });
        }).finally(() => {
          this.loading = false;
        });
      },

      // 刷新数据
      refresh() {
        this.query();
      },

      // 分页变化
      handleCurrentChange(val) {
        this.search.pageNumber = val;
        this.query();
      },

      handleSizeChange(val) {
        this.search.pageSize = val;
        this.search.pageNumber = 1;
        this.query();
      },

      // 选择变化
      handleSelectionChange(val) {
        this.selectedRows = val;
      },

      // 新增
      add() {
        this.addVisible = true;
      },

      addFalse() {
        this.addVisible = false;
        this.query();
      },

      // 修改
      updateData(id) {
        this.updateId = id;
        this.updateVisible = true;
      },

      updateDataBtn() {
        if (this.selectedRows.length === 1) {
          this.updateData(this.selectedRows[0].id);
        }
      },

      updateFalse() {
        this.updateId = '';
        this.updateVisible = false;
        this.query();
      },

      // 删除
      deleteDataBtn() {
        const count = this.selectedRows.length;
        this.$confirm(`确定删除选中的 ${count} 条数据吗？`, '删除确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          customClass: 'modern-confirm'
        }).then(() => {
          const ids = this.selectedRows.map(row => row.id).join(",");
          this.deleteDate(ids);
        });
      },

      deleteDate(ids) {
        removeSysAttractions({ ids }).then(res => {
          if (res.code === 1000) {
            this.$message.success('删除成功！');
            this.query();
          } else {
            this.$notify.error({
              title: '错误',
              message: res.message || '删除失败'
            });
          }
        }).catch(error => {
          console.error('删除错误:', error);
          this.$message.error('网络错误，请稍后重试');
        });
      },

      // 格式化时间
      formatTime(time) {
        if (!time) return '-';
        return new Date(time).toLocaleString('zh-CN');
      },

      // 导出数据
      exportData() {
        this.$message.info('导出功能开发中...');
      },

      // 显示列设置
      showColumnSettings() {
        this.$message.info('列设置功能开发中...');
      }
    },

    mounted() {
      this.query();
    }
  }
</script>
</script>
<style lang="scss" scoped>
  .modern-attractions {
    padding: 24px;
    background: var(--bg-primary);
    min-height: 100vh;
  }

  // 页面头部
  .page-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      padding: 24px;
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 16px;
      box-shadow: var(--shadow-md);
    }

    .title-section {
      .page-title {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 700;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 12px;

        i {
          font-size: 32px;
          background: var(--primary-gradient);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .page-description {
        margin: 0;
        color: var(--text-secondary);
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;

      .modern-btn {
        padding: 12px 24px;
        border-radius: 12px;
        font-weight: 500;
        transition: all var(--transition-normal) ease;
        border: none;

        &.el-button--primary {
          background: var(--primary-gradient);

          &:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
          }
        }

        &:not(.el-button--primary) {
          background: var(--glass-bg);
          backdrop-filter: blur(10px);
          border: 1px solid var(--glass-border);
          color: var(--text-primary);

          &:hover {
            background: var(--primary-gradient);
            color: white;
            transform: translateY(-2px);
          }
        }
      }
    }
  }

  // 搜索区域
  .search-section {
    margin-bottom: 24px;

    .search-card {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 16px;
      box-shadow: var(--shadow-md);
      overflow: hidden;
    }

    .search-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid var(--glass-border);
      background: rgba(255, 255, 255, 0.5);

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: var(--primary-color);
        }
      }

      .collapse-btn {
        color: var(--text-secondary);
        transition: all var(--transition-normal) ease;

        &.collapsed {
          transform: rotate(180deg);
        }

        &:hover {
          color: var(--primary-color);
        }
      }
    }

    .search-content {
      padding: 24px;
    }

    .search-form {
      .el-form-item {
        margin-bottom: 20px;

        :deep(.el-form-item__label) {
          color: var(--text-primary);
          font-weight: 500;
        }
      }

      .search-input {
        width: 200px;

        :deep(.el-input__inner) {
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          border-radius: 8px;
          transition: all var(--transition-normal) ease;

          &:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
          }
        }
      }

      .search-select {
        width: 150px;

        :deep(.el-input__inner) {
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          border-radius: 8px;
        }
      }

      .price-input {
        width: 120px;

        :deep(.el-input__inner) {
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          border-radius: 8px;
        }
      }

      .price-separator {
        margin: 0 8px;
        color: var(--text-secondary);
      }

      .search-btn {
        background: var(--primary-gradient);
        border: none;
        border-radius: 8px;
        padding: 10px 20px;

        &:hover {
          transform: translateY(-1px);
          box-shadow: var(--shadow-md);
        }
      }

      .reset-btn {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 8px;
        color: var(--text-primary);

        &:hover {
          background: var(--text-secondary);
          color: white;
        }
      }
    }
  }

  // 统计卡片
  .stats-section {
    margin-bottom: 24px;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;
    }

    .stat-card {
      display: flex;
      align-items: center;
      padding: 20px;
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 16px;
      box-shadow: var(--shadow-sm);
      transition: all var(--transition-normal) ease;

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);
      }

      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        i {
          font-size: 24px;
          color: white;
        }

        &.total {
          background: var(--primary-gradient);
        }

        &.active {
          background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        }

        &.inactive {
          background: linear-gradient(135deg, #ff9a56 0%, #ffad56 100%);
        }

        &.selected {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
      }

      .stat-content {
        .stat-number {
          font-size: 24px;
          font-weight: 700;
          color: var(--text-primary);
          line-height: 1;
        }

        .stat-label {
          font-size: 12px;
          color: var(--text-secondary);
          margin-top: 4px;
        }
      }
    }
  }

  // 表格区域
  .table-section {
    .table-card {
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 16px;
      box-shadow: var(--shadow-md);
      overflow: hidden;
    }

    .table-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid var(--glass-border);
      background: rgba(255, 255, 255, 0.5);

      .toolbar-left {
        display: flex;
        gap: 12px;

        .toolbar-btn {
          padding: 10px 16px;
          border-radius: 8px;
          font-weight: 500;
          transition: all var(--transition-normal) ease;
          border: none;

          &.el-button--primary {
            background: var(--primary-gradient);
          }

          &.el-button--success {
            background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
          }

          &.el-button--danger {
            background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
          }

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }

      .toolbar-right {
        display: flex;
        gap: 8px;

        .icon-btn {
          width: 36px;
          height: 36px;
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          color: var(--text-secondary);
          transition: all var(--transition-normal) ease;

          &:hover {
            background: var(--primary-gradient);
            color: white;
            transform: scale(1.1);
          }
        }
      }
    }

    .table-container {
      padding: 24px;

      .modern-table {
        border-radius: 12px;
        overflow: hidden;

        :deep(.el-table__header) {
          th {
            background: var(--glass-bg) !important;
            color: var(--text-primary) !important;
            font-weight: 600;
            border-bottom: 2px solid var(--glass-border);
          }
        }

        :deep(.el-table__body) {
          tr {
            transition: all var(--transition-fast) ease;

            &:hover {
              background: rgba(102, 126, 234, 0.05) !important;
            }

            td {
              border-bottom: 1px solid var(--border-color);
              padding: 16px 12px;
            }
          }
        }
      }
    }
  }

  // 表格单元格样式
  .name-cell {
    display: flex;
    align-items: center;
    gap: 12px;

    .attraction-avatar {
      flex-shrink: 0;
      border: 2px solid var(--glass-border);
    }

    .name-info {
      .name-text {
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 2px;
      }

      .name-subtitle {
        font-size: 12px;
        color: var(--text-secondary);
      }
    }
  }

  .price-cell {
    display: flex;
    align-items: center;
    justify-content: center;

    .price-symbol {
      font-size: 12px;
      color: var(--text-secondary);
      margin-right: 2px;
    }

    .price-value {
      font-size: 16px;
      font-weight: 600;
      color: var(--primary-color);
    }
  }

  .intro-cell {
    color: var(--text-secondary);
    line-height: 1.4;
  }

  .stock-tag {
    font-weight: 600;
    border-radius: 6px;
  }

  .time-cell {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--text-secondary);

    i {
      color: var(--primary-color);
    }
  }

  .action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;

    .action-btn {
      transition: all var(--transition-normal) ease;

      &:hover {
        transform: scale(1.1);
      }
    }
  }

  // 分页容器
  .pagination-container {
    padding: 20px 24px;
    border-top: 1px solid var(--glass-border);
    background: rgba(255, 255, 255, 0.5);

    .modern-pagination {
      display: flex;
      justify-content: flex-end;
    }
  }

  // 响应式设计
  @media (max-width: 1024px) {
    .modern-attractions {
      padding: 16px;
    }

    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .table-toolbar {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }
  }

  @media (max-width: 768px) {
    .stats-grid {
      grid-template-columns: 1fr;
    }

    .search-form {
      .el-form-item {
        display: block;

        .search-input,
        .search-select,
        .price-input {
          width: 100%;
        }
      }
    }

    .toolbar-left {
      flex-wrap: wrap;
    }
  }
</style>