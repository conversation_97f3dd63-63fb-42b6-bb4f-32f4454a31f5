<template>
  <div class="modern-dashboard">
    <!-- 页面头部 -->
    <div class="dashboard-header">
      <div class="header-content">
        <div class="welcome-section">
          <h1 class="dashboard-title">
            <i class="el-icon-data-analysis"></i>
            数据概览
          </h1>
          <p class="dashboard-subtitle">实时监控旅游业务数据，洞察运营趋势</p>
        </div>

        <div class="time-filter">
          <el-select v-model="timeRange" @change="handleTimeRangeChange" class="time-selector">
            <el-option label="近7天" value="7"></el-option>
            <el-option label="近15天" value="15"></el-option>
            <el-option label="近30天" value="30"></el-option>
          </el-select>
          <el-button icon="el-icon-refresh" @click="refreshData" class="refresh-btn">刷新</el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-overview">
      <div class="stats-grid">
        <div class="stat-card" v-for="(stat, index) in statsData" :key="index">
          <div class="stat-icon" :class="stat.type">
            <i :class="stat.icon"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
            <div class="stat-trend" :class="stat.trend > 0 ? 'up' : 'down'">
              <i :class="stat.trend > 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              <span>{{ Math.abs(stat.trend) }}%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="charts-grid">
        <!-- 景点预约趋势图 -->
        <div class="chart-card attractions-chart">
          <div class="chart-header">
            <div class="chart-title">
              <i class="el-icon-place"></i>
              <span>景点预约趋势</span>
            </div>
            <div class="chart-actions">
              <el-tooltip content="全屏查看" placement="top">
                <el-button icon="el-icon-full-screen" circle size="mini" @click="fullscreenChart('attractions')"></el-button>
              </el-tooltip>
              <el-tooltip content="导出图表" placement="top">
                <el-button icon="el-icon-download" circle size="mini" @click="exportChart('attractions')"></el-button>
              </el-tooltip>
            </div>
          </div>
          <div class="chart-container">
            <div
              id="attractionsChart"
              class="chart-content"
              v-loading="loading"
              element-loading-text="加载中..."
              element-loading-spinner="el-icon-loading"
            ></div>
          </div>
          <div class="chart-footer">
            <div class="chart-summary">
              <span class="summary-item">
                <i class="el-icon-arrow-up"></i>
                总预约: {{ attractionsTotalBookings }}
              </span>
              <span class="summary-item">
                <i class="el-icon-trend-charts"></i>
                平均: {{ attractionsAvgBookings }}
              </span>
            </div>
          </div>
        </div>

        <!-- 酒店预约趋势图 -->
        <div class="chart-card hotels-chart">
          <div class="chart-header">
            <div class="chart-title">
              <i class="el-icon-s-home"></i>
              <span>酒店预约趋势</span>
            </div>
            <div class="chart-actions">
              <el-tooltip content="全屏查看" placement="top">
                <el-button icon="el-icon-full-screen" circle size="mini" @click="fullscreenChart('hotels')"></el-button>
              </el-tooltip>
              <el-tooltip content="导出图表" placement="top">
                <el-button icon="el-icon-download" circle size="mini" @click="exportChart('hotels')"></el-button>
              </el-tooltip>
            </div>
          </div>
          <div class="chart-container">
            <div
              id="hotelsChart"
              class="chart-content"
              v-loading="loading"
              element-loading-text="加载中..."
              element-loading-spinner="el-icon-loading"
            ></div>
          </div>
          <div class="chart-footer">
            <div class="chart-summary">
              <span class="summary-item">
                <i class="el-icon-arrow-up"></i>
                总预约: {{ hotelsTotalBookings }}
              </span>
              <span class="summary-item">
                <i class="el-icon-trend-charts"></i>
                平均: {{ hotelsAvgBookings }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 综合对比图表 -->
    <div class="comparison-section">
      <div class="comparison-card">
        <div class="chart-header">
          <div class="chart-title">
            <i class="el-icon-s-data"></i>
            <span>预约数据对比分析</span>
          </div>
          <div class="chart-actions">
            <el-radio-group v-model="comparisonType" @change="updateComparisonChart" size="mini">
              <el-radio-button label="line">趋势图</el-radio-button>
              <el-radio-button label="bar">柱状图</el-radio-button>
              <el-radio-button label="area">面积图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-container large">
          <div
            id="comparisonChart"
            class="chart-content"
            v-loading="loading"
            element-loading-text="加载中..."
            element-loading-spinner="el-icon-loading"
          ></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getManageData } from '../../../api/api'
  import * as echarts from "echarts"

  export default {
    name: "ModernDashboard",
    data() {
      return {
        loading: false,
        timeRange: '7',
        comparisonType: 'line',
        rawData: {},

        // 图表实例
        attractionsChart: null,
        hotelsChart: null,
        comparisonChart: null,

        // 统计数据
        statsData: [
          {
            type: 'attractions',
            icon: 'el-icon-place',
            label: '景点预约',
            value: 0,
            trend: 0
          },
          {
            type: 'hotels',
            icon: 'el-icon-s-home',
            label: '酒店预约',
            value: 0,
            trend: 0
          },
          {
            type: 'revenue',
            icon: 'el-icon-coin',
            label: '总收入',
            value: 0,
            trend: 0
          },
          {
            type: 'users',
            icon: 'el-icon-user',
            label: '活跃用户',
            value: 0,
            trend: 0
          }
        ]
      }
    },

    computed: {
      // 景点预约统计
      attractionsTotalBookings() {
        if (!this.rawData.nums) return 0;
        return this.rawData.nums.reduce((sum, num) => sum + num, 0);
      },

      attractionsAvgBookings() {
        if (!this.rawData.nums || this.rawData.nums.length === 0) return 0;
        return Math.round(this.attractionsTotalBookings / this.rawData.nums.length);
      },

      // 酒店预约统计
      hotelsTotalBookings() {
        if (!this.rawData.orders) return 0;
        return this.rawData.orders.reduce((sum, num) => sum + num, 0);
      },

      hotelsAvgBookings() {
        if (!this.rawData.orders || this.rawData.orders.length === 0) return 0;
        return Math.round(this.hotelsTotalBookings / this.rawData.orders.length);
      }
    },

    methods: {
      // 初始化数据
      async init() {
        this.loading = true;
        try {
          console.log('🔍 开始获取管理数据...');
          const res = await getManageData();
          console.log('📥 管理数据API响应:', res);

          if (res.code === 1000) {
            this.rawData = res.data;
            console.log('📊 数据加载完成:');
            console.log('  - 日期数组:', res.data.dates);
            console.log('  - 景点预约数组:', res.data.nums);
            console.log('  - 酒店预约数组:', res.data.orders);
            console.log('🕐 时间轴展示 (最新→最旧):');
            console.log('  最新日期:', res.data.dates[0]);
            console.log('  最旧日期:', res.data.dates[res.data.dates.length - 1]);

            this.updateStatsData();
            this.initCharts();
          } else {
            this.$message.error('获取数据失败');
          }
        } catch (error) {
          console.error('数据加载错误:', error);
          this.$message.error('网络错误，请稍后重试');
        } finally {
          this.loading = false;
        }
      },

      // 更新统计数据
      updateStatsData() {
        this.statsData[0].value = this.attractionsTotalBookings;
        this.statsData[0].trend = this.calculateTrend(this.rawData.nums);

        this.statsData[1].value = this.hotelsTotalBookings;
        this.statsData[1].trend = this.calculateTrend(this.rawData.orders);

        // 模拟其他数据
        this.statsData[2].value = (this.attractionsTotalBookings * 150 + this.hotelsTotalBookings * 300).toLocaleString();
        this.statsData[2].trend = Math.random() * 20 - 10;

        this.statsData[3].value = Math.floor(Math.random() * 1000 + 500);
        this.statsData[3].trend = Math.random() * 15 - 5;
      },

      // 计算趋势
      calculateTrend(data) {
        if (!data || data.length < 2) return 0;
        const recent = data.slice(-3).reduce((sum, num) => sum + num, 0) / 3;
        const previous = data.slice(-6, -3).reduce((sum, num) => sum + num, 0) / 3;
        return previous === 0 ? 0 : Math.round(((recent - previous) / previous) * 100);
      },

      // 初始化所有图表
      initCharts() {
        this.$nextTick(() => {
          this.initAttractionsChart();
          this.initHotelsChart();
          this.initComparisonChart();
          this.handleResize();
        });
      },

      // 初始化景点预约图表
      initAttractionsChart() {
        const chartDom = document.getElementById('attractionsChart');
        if (!chartDom) return;

        this.attractionsChart = echarts.init(chartDom);
        const option = this.getModernChartOption('景点预约', this.rawData.nums, '#667eea');
        this.attractionsChart.setOption(option);

        // 添加点击事件
        this.attractionsChart.on('click', (params) => {
          this.$message.info(`${params.name}: ${params.value} 次预约`);
        });
      },

      // 初始化酒店预约图表
      initHotelsChart() {
        const chartDom = document.getElementById('hotelsChart');
        if (!chartDom) return;

        this.hotelsChart = echarts.init(chartDom);
        const option = this.getModernChartOption('酒店预约', this.rawData.orders, '#f093fb');
        this.hotelsChart.setOption(option);

        // 添加点击事件
        this.hotelsChart.on('click', (params) => {
          this.$message.info(`${params.name}: ${params.value} 次预约`);
        });
      },

      // 初始化对比图表
      initComparisonChart() {
        const chartDom = document.getElementById('comparisonChart');
        if (!chartDom) return;

        this.comparisonChart = echarts.init(chartDom);
        this.updateComparisonChart();
      },

      // 获取现代化图表配置
      getModernChartOption(title, data, color) {
        return {
          title: {
            show: false
          },
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderColor: color,
            borderWidth: 1,
            textStyle: {
              color: '#333'
            },
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: color,
                opacity: 0.6
              }
            },
            formatter: (params) => {
              const param = params[0];
              return `
                <div style="padding: 8px;">
                  <div style="font-weight: 600; margin-bottom: 4px;">${param.name}</div>
                  <div style="color: ${color};">
                    <i class="el-icon-arrow-up"></i>
                    ${title}: ${param.value} 次
                  </div>
                </div>
              `;
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '8%',
            top: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: this.rawData.dates || [],
            axisLine: {
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.3)'
              }
            },
            axisLabel: {
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: 12
            },
            splitLine: {
              show: false
            }
          },
          yAxis: {
            type: 'value',
            axisLine: {
              show: false
            },
            axisLabel: {
              color: 'rgba(255, 255, 255, 0.8)',
              fontSize: 12
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.1)',
                type: 'dashed'
              }
            }
          },
          series: [
            {
              name: title,
              type: 'line',
              smooth: true,
              symbol: 'circle',
              symbolSize: 8,
              lineStyle: {
                width: 3,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  { offset: 0, color: color },
                  { offset: 1, color: color + '80' }
                ])
              },
              itemStyle: {
                color: color,
                borderColor: '#fff',
                borderWidth: 2
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: color + '40' },
                  { offset: 1, color: color + '10' }
                ])
              },
              emphasis: {
                focus: 'series',
                itemStyle: {
                  shadowBlur: 10,
                  shadowColor: color
                }
              },
              data: data || []
            }
          ],
          animation: true,
          animationDuration: 1000,
          animationEasing: 'cubicOut'
        };
      },

      // 更新对比图表
      updateComparisonChart() {
        if (!this.comparisonChart) return;

        const option = {
          title: {
            show: false
          },
          tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderColor: '#667eea',
            borderWidth: 1,
            textStyle: {
              color: '#333'
            }
          },
          legend: {
            data: ['景点预约', '酒店预约'],
            textStyle: {
              color: 'rgba(255, 255, 255, 0.9)'
            },
            top: '5%'
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '8%',
            top: '15%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            boundaryGap: this.comparisonType === 'bar',
            data: this.rawData.dates || [],
            axisLine: {
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.3)'
              }
            },
            axisLabel: {
              color: 'rgba(255, 255, 255, 0.8)'
            }
          },
          yAxis: {
            type: 'value',
            axisLine: {
              show: false
            },
            axisLabel: {
              color: 'rgba(255, 255, 255, 0.8)'
            },
            splitLine: {
              lineStyle: {
                color: 'rgba(255, 255, 255, 0.1)',
                type: 'dashed'
              }
            }
          },
          series: [
            {
              name: '景点预约',
              type: this.comparisonType,
              smooth: true,
              data: this.rawData.nums || [],
              ...this.getSeriesStyle('#667eea', this.comparisonType)
            },
            {
              name: '酒店预约',
              type: this.comparisonType,
              smooth: true,
              data: this.rawData.orders || [],
              ...this.getSeriesStyle('#f093fb', this.comparisonType)
            }
          ],
          animation: true,
          animationDuration: 1000
        };

        this.comparisonChart.setOption(option, true);
      },

      // 获取系列样式
      getSeriesStyle(color, type) {
        const baseStyle = {
          itemStyle: {
            color: color
          }
        };

        if (type === 'line' || type === 'area') {
          return {
            ...baseStyle,
            lineStyle: {
              width: 3,
              color: color
            },
            areaStyle: type === 'area' ? {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: color + '40' },
                { offset: 1, color: color + '10' }
              ])
            } : undefined
          };
        }

        return baseStyle;
      },

      // 处理时间范围变化
      handleTimeRangeChange() {
        this.$message.info(`切换到近${this.timeRange}天数据`);
        // 这里可以重新请求数据
        this.refreshData();
      },

      // 刷新数据
      refreshData() {
        this.init();
      },

      // 全屏显示图表
      fullscreenChart(type) {
        this.$message.info(`${type === 'attractions' ? '景点' : '酒店'}图表全屏功能开发中...`);
      },

      // 导出图表
      exportChart(type) {
        const chart = type === 'attractions' ? this.attractionsChart : this.hotelsChart;
        if (chart) {
          const url = chart.getDataURL({
            type: 'png',
            backgroundColor: '#fff'
          });
          const link = document.createElement('a');
          link.download = `${type === 'attractions' ? '景点' : '酒店'}预约趋势图.png`;
          link.href = url;
          link.click();
        }
      },

      // 处理窗口大小变化
      handleResize() {
        setTimeout(() => {
          if (this.attractionsChart) {
            this.attractionsChart.resize();
          }
          if (this.hotelsChart) {
            this.hotelsChart.resize();
          }
          if (this.comparisonChart) {
            this.comparisonChart.resize();
          }
        }, 100);
      }
    },

    created() {
      // 预加载数据
    },

    mounted() {
      this.init();

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize);
    },

    beforeDestroy() {
      // 清理图表实例
      if (this.attractionsChart) {
        this.attractionsChart.dispose();
      }
      if (this.hotelsChart) {
        this.hotelsChart.dispose();
      }
      if (this.comparisonChart) {
        this.comparisonChart.dispose();
      }

      // 移除事件监听
      window.removeEventListener('resize', this.handleResize);
    }
  }
</script>
</script>

<style lang="scss" scoped>
  .modern-dashboard {
    padding: 24px;
    background: var(--bg-primary);
    min-height: 100vh;
    overflow-x: hidden;
  }

  // 页面头部
  .dashboard-header {
    margin-bottom: 24px;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px;
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 16px;
      box-shadow: var(--shadow-md);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--primary-gradient);
        opacity: 0.05;
        z-index: 0;
      }

      > * {
        position: relative;
        z-index: 1;
      }
    }

    .welcome-section {
      .dashboard-title {
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 700;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 12px;

        i {
          font-size: 32px;
          background: var(--primary-gradient);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }

      .dashboard-subtitle {
        margin: 0;
        color: var(--text-secondary);
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .time-filter {
      display: flex;
      align-items: center;
      gap: 12px;

      .time-selector {
        width: 120px;

        :deep(.el-input__inner) {
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          border-radius: 8px;
          color: var(--text-primary);
        }
      }

      .refresh-btn {
        background: var(--glass-bg);
        border: 1px solid var(--glass-border);
        border-radius: 8px;
        color: var(--text-primary);
        transition: all var(--transition-normal) ease;

        &:hover {
          background: var(--primary-gradient);
          color: white;
          transform: translateY(-2px);
          box-shadow: var(--shadow-md);
        }
      }
    }
  }

  // 统计概览
  .stats-overview {
    margin-bottom: 24px;

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
    }

    .stat-card {
      display: flex;
      align-items: center;
      padding: 24px;
      background: var(--glass-bg);
      backdrop-filter: blur(20px);
      border: 1px solid var(--glass-border);
      border-radius: 16px;
      box-shadow: var(--shadow-sm);
      transition: all var(--transition-normal) ease;
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--primary-gradient);
        opacity: 0.1;
        transition: left var(--transition-normal) ease;
        z-index: 0;
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-lg);

        &::before {
          left: 0;
        }
      }

      > * {
        position: relative;
        z-index: 1;
      }

      .stat-icon {
        width: 56px;
        height: 56px;
        border-radius: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        flex-shrink: 0;

        i {
          font-size: 28px;
          color: white;
        }

        &.attractions {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        &.hotels {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.revenue {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        &.users {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }

      .stat-content {
        flex: 1;

        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: var(--text-primary);
          line-height: 1.2;
          margin-bottom: 4px;
        }

        .stat-label {
          font-size: 14px;
          color: var(--text-secondary);
          margin-bottom: 8px;
        }

        .stat-trend {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          font-weight: 600;

          &.up {
            color: #67c23a;
          }

          &.down {
            color: #f56c6c;
          }

          i {
            font-size: 12px;
          }
        }
      }
    }
  }

  // 图表区域
  .charts-section {
    margin-bottom: 24px;

    .charts-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
      gap: 24px;
    }
  }

  // 图表卡片
  .chart-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal) ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
    }

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 24px;
      border-bottom: 1px solid var(--glass-border);
      background: rgba(255, 255, 255, 0.5);

      .chart-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);

        i {
          font-size: 18px;
          color: var(--primary-color);
        }
      }

      .chart-actions {
        display: flex;
        gap: 8px;

        .el-button {
          background: var(--glass-bg);
          border: 1px solid var(--glass-border);
          color: var(--text-secondary);
          transition: all var(--transition-normal) ease;

          &:hover {
            background: var(--primary-gradient);
            color: white;
            transform: scale(1.1);
          }
        }

        .el-radio-group {
          :deep(.el-radio-button__inner) {
            background: var(--glass-bg);
            border-color: var(--glass-border);
            color: var(--text-primary);
            transition: all var(--transition-normal) ease;

            &:hover {
              color: var(--primary-color);
            }
          }

          :deep(.el-radio-button__orig-radio:checked + .el-radio-button__inner) {
            background: var(--primary-gradient);
            border-color: var(--primary-color);
            color: white;
            box-shadow: var(--shadow-sm);
          }
        }
      }
    }

    .chart-container {
      padding: 24px;
      position: relative;

      &.large {
        padding: 32px;
      }

      .chart-content {
        width: 100%;
        height: 300px;
        background: var(--dark-gradient);
        border-radius: 12px;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: var(--primary-gradient);
          opacity: 0.1;
          z-index: 0;
        }
      }

      &.large .chart-content {
        height: 400px;
      }
    }

    .chart-footer {
      padding: 16px 24px;
      border-top: 1px solid var(--glass-border);
      background: rgba(255, 255, 255, 0.3);

      .chart-summary {
        display: flex;
        gap: 24px;

        .summary-item {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 14px;
          color: var(--text-secondary);

          i {
            color: var(--primary-color);
          }
        }
      }
    }
  }

  // 对比分析区域
  .comparison-section {
    .comparison-card {
      @extend .chart-card;
    }
  }

  // 加载状态
  :deep(.el-loading-mask) {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    border-radius: 12px;
  }

  :deep(.el-loading-spinner) {
    .el-icon-loading {
      font-size: 32px;
      color: var(--primary-color);
    }

    .el-loading-text {
      color: var(--text-primary);
      font-weight: 500;
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .charts-grid {
      grid-template-columns: 1fr;
    }
  }

  @media (max-width: 768px) {
    .modern-dashboard {
      padding: 16px;
    }

    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;
    }

    .stats-grid {
      grid-template-columns: repeat(2, 1fr);
    }

    .chart-header {
      flex-direction: column;
      gap: 12px;
      align-items: stretch;
    }

    .chart-container {
      padding: 16px;

      .chart-content {
        height: 250px;
      }

      &.large .chart-content {
        height: 300px;
      }
    }

    .chart-summary {
      flex-direction: column;
      gap: 8px;
    }
  }

  @media (max-width: 480px) {
    .stats-grid {
      grid-template-columns: 1fr;
    }

    .stat-card {
      padding: 16px;

      .stat-icon {
        width: 48px;
        height: 48px;
        margin-right: 16px;

        i {
          font-size: 24px;
        }
      }

      .stat-value {
        font-size: 20px;
      }
    }
  }
</style>