<template>
  <div class="modern-login">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>

    <!-- 左侧背景区域 -->
    <div class="login-background">
      <div class="background-content">
        <div class="welcome-text">
          <h1>探索世界之美</h1>
          <p>让每一次旅行都成为难忘的回忆</p>
        </div>
        <div class="feature-list">
          <div class="feature-item">
            <i class="el-icon-location"></i>
            <span>精选景点推荐</span>
          </div>
          <div class="feature-item">
            <i class="el-icon-s-home"></i>
            <span>优质酒店预订</span>
          </div>
          <div class="feature-item">
            <i class="el-icon-guide"></i>
            <span>专业路线规划</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧登录表单 -->
    <div class="login-form-container">
      <div class="login-form-wrapper">
        <!-- Logo和标题 -->
        <div class="login-header">
          <div class="logo-container">
            <img src="../../../assets/image/logo1.png" class="logo-image" alt="Logo">
            <div class="logo-text">
              <h2>旅游推荐平台</h2>
              <span>Tourism Management System</span>
            </div>
          </div>
        </div>

        <!-- 登录表单 -->
        <div class="login-form">
          <div class="form-title">
            <h3>欢迎回来</h3>
            <p>请登录您的管理员账户</p>
          </div>

          <el-form :model="loginForm" :rules="loginRules" ref="loginForm" class="modern-form">
            <el-form-item prop="loginAccount">
              <div class="input-wrapper">
                <i class="input-icon el-icon-user"></i>
                <el-input
                  v-model="loginForm.loginAccount"
                  placeholder="请输入登录账号"
                  class="modern-input"
                  @keyup.enter.native="handleLogin"
                >
                </el-input>
              </div>
            </el-form-item>

            <el-form-item prop="password">
              <div class="input-wrapper">
                <i class="input-icon el-icon-lock"></i>
                <el-input
                  v-model="loginForm.password"
                  type="password"
                  placeholder="请输入登录密码"
                  class="modern-input"
                  show-password
                  @keyup.enter.native="handleLogin"
                >
                </el-input>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                class="login-button"
                :loading="loginLoading"
                @click="handleLogin"
              >
                <span v-if="!loginLoading">立即登录</span>
                <span v-else>登录中...</span>
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 底部信息 -->
          <div class="login-footer">
            <p>© 2025 旅游推荐平台 - 让旅游更简单</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { login, getUser } from '../../../api/api'

  export default {
    name: "ModernLogin",
    data() {
      return {
        loginForm: {
          loginAccount: '',
          password: ''
        },
        loginLoading: false,
        loginRules: {
          loginAccount: [
            { required: true, message: '请输入登录账号', trigger: 'blur' },
            { min: 3, max: 20, message: '账号长度在 3 到 20 个字符', trigger: 'blur' }
          ],
          password: [
            { required: true, message: '请输入登录密码', trigger: 'blur' },
            { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
          ]
        }
      }
    },
    methods: {
      // 处理登录
      handleLogin() {
        this.$refs.loginForm.validate((valid) => {
          if (valid) {
            this.performLogin();
          } else {
            this.$message.warning('请检查输入信息');
            return false;
          }
        });
      },

      // 执行登录
      performLogin() {
        this.loginLoading = true;

        const params = {
          loginAccount: this.loginForm.loginAccount,
          password: this.loginForm.password
        };

        login(params).then(res => {
          if (res.code === 1000) {
            this.$message.success('登录成功，正在跳转...');

            // 保存token
            this.$store.commit('user/setToken', res.data.token);

            // 获取用户信息
            this.getUserInfo();

            // 添加登录成功动画效果
            this.animateLoginSuccess();

            // 延迟跳转，让用户看到成功提示
            setTimeout(() => {
              this.$router.push("/index");
            }, 1500);

          } else {
            this.$message.error(res.message || '登录失败');
          }
        }).catch(error => {
          console.error('登录错误:', error);
          this.$message.error('网络错误，请稍后重试');
        }).finally(() => {
          this.loginLoading = false;
        });
      },

      // 获取用户信息
      getUserInfo() {
        getUser().then(res => {
          if (res.code === 1000) {
            this.$store.commit('user/setUser', JSON.stringify(res.data));
          }
        }).catch(error => {
          console.error('获取用户信息错误:', error);
        });
      },

      // 登录成功动画
      animateLoginSuccess() {
        const formWrapper = document.querySelector('.login-form-wrapper');
        if (formWrapper) {
          formWrapper.style.transform = 'scale(1.02)';
          formWrapper.style.boxShadow = '0 20px 40px rgba(102, 126, 234, 0.3)';

          setTimeout(() => {
            formWrapper.style.transform = 'scale(1)';
            formWrapper.style.boxShadow = 'var(--glass-shadow)';
          }, 300);
        }
      },

      // 初始化动画
      initAnimations() {
        // 背景装饰动画
        const shapes = document.querySelectorAll('.floating-shape');
        shapes.forEach((shape, index) => {
          shape.style.animationDelay = `${index * 0.5}s`;
        });

        // 表单进入动画
        const formWrapper = document.querySelector('.login-form-wrapper');
        if (formWrapper) {
          formWrapper.style.opacity = '0';
          formWrapper.style.transform = 'translateY(30px)';

          setTimeout(() => {
            formWrapper.style.transition = 'all 0.8s ease';
            formWrapper.style.opacity = '1';
            formWrapper.style.transform = 'translateY(0)';
          }, 300);
        }

        // 背景内容动画
        const backgroundContent = document.querySelector('.background-content');
        if (backgroundContent) {
          backgroundContent.style.opacity = '0';
          backgroundContent.style.transform = 'translateX(-30px)';

          setTimeout(() => {
            backgroundContent.style.transition = 'all 0.8s ease';
            backgroundContent.style.opacity = '1';
            backgroundContent.style.transform = 'translateX(0)';
          }, 500);
        }
      }
    },

    mounted() {
      // 初始化动画
      this.$nextTick(() => {
        this.initAnimations();
      });

      // 自动聚焦到账号输入框
      setTimeout(() => {
        const accountInput = document.querySelector('.modern-input input');
        if (accountInput) {
          accountInput.focus();
        }
      }, 1000);
    }
  }
</script>
</script>

<style lang="scss" scoped>
  .modern-login {
    width: 100vw;
    height: 100vh;
    display: flex;
    position: relative;
    overflow: hidden;
    background: var(--primary-gradient);
  }

  // 背景装饰
  .background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
  }

  .floating-shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    animation: float 6s ease-in-out infinite;

    &.shape-1 {
      width: 80px;
      height: 80px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }

    &.shape-2 {
      width: 120px;
      height: 120px;
      top: 60%;
      left: 5%;
      animation-delay: 2s;
    }

    &.shape-3 {
      width: 60px;
      height: 60px;
      top: 20%;
      right: 15%;
      animation-delay: 4s;
    }

    &.shape-4 {
      width: 100px;
      height: 100px;
      bottom: 20%;
      right: 10%;
      animation-delay: 1s;
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-20px) rotate(180deg);
    }
  }

  // 左侧背景区域
  .login-background {
    flex: 1;
    background: url('../../../assets/image/lv2.jpg') center/cover;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: var(--primary-gradient);
      opacity: 0.8;
    }
  }

  .background-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    padding: 40px;

    .welcome-text {
      margin-bottom: 60px;

      h1 {
        font-size: 48px;
        font-weight: 700;
        margin: 0 0 20px 0;
        background: var(--soft-gradient);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
      }

      p {
        font-size: 20px;
        margin: 0;
        opacity: 0.9;
        letter-spacing: 1px;
      }
    }

    .feature-list {
      display: flex;
      flex-direction: column;
      gap: 24px;

      .feature-item {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 16px 24px;
        background: var(--glass-bg);
        backdrop-filter: blur(15px);
        border: 1px solid var(--glass-border);
        border-radius: 16px;
        transition: all var(--transition-normal) ease;

        &:hover {
          transform: translateX(10px);
          box-shadow: var(--shadow-lg);
        }

        i {
          font-size: 24px;
          color: var(--accent-color);
        }

        span {
          font-size: 16px;
          font-weight: 500;
        }
      }
    }
  }

  // 右侧登录表单容器
  .login-form-container {
    width: 480px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    position: relative;
    z-index: 10;
  }

  .login-form-wrapper {
    width: 100%;
    max-width: 400px;
    padding: 40px;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: 24px;
    box-shadow: var(--glass-shadow);
    margin: 40px;
  }

  // Logo和标题区域
  .login-header {
    text-align: center;
    margin-bottom: 40px;

    .logo-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;

      .logo-image {
        width: 64px;
        height: 64px;
        border-radius: 16px;
        object-fit: cover;
        box-shadow: var(--shadow-md);
        transition: all var(--transition-normal) ease;

        &:hover {
          transform: scale(1.05) rotate(5deg);
        }
      }

      .logo-text {
        text-align: center;

        h2 {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 700;
          background: var(--primary-gradient);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
        }

        span {
          font-size: 12px;
          color: var(--text-secondary);
          letter-spacing: 1px;
          text-transform: uppercase;
        }
      }
    }
  }

  // 表单标题
  .form-title {
    text-align: center;
    margin-bottom: 32px;

    h3 {
      margin: 0 0 8px 0;
      font-size: 28px;
      font-weight: 600;
      color: var(--text-primary);
    }

    p {
      margin: 0;
      font-size: 14px;
      color: var(--text-secondary);
    }
  }

  // 现代化表单
  .modern-form {
    .el-form-item {
      margin-bottom: 24px;

      .el-form-item__error {
        color: #ff4757;
        font-size: 12px;
        margin-top: 4px;
      }
    }
  }

  .input-wrapper {
    position: relative;

    .input-icon {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      font-size: 18px;
      color: var(--text-secondary);
      z-index: 10;
      transition: color var(--transition-fast) ease;
    }

    .modern-input {
      :deep(.el-input__inner) {
        height: 52px;
        padding-left: 48px;
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        border: 2px solid var(--glass-border);
        border-radius: 16px;
        font-size: 16px;
        color: var(--text-primary);
        transition: all var(--transition-normal) ease;

        &:focus {
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
          background: rgba(255, 255, 255, 0.9);
        }

        &::placeholder {
          color: var(--text-secondary);
          font-size: 14px;
        }
      }

      &:hover .input-icon {
        color: var(--primary-color);
      }
    }

    &:focus-within .input-icon {
      color: var(--primary-color);
      transform: translateY(-50%) scale(1.1);
    }
  }

  // 登录按钮
  .login-button {
    width: 100%;
    height: 52px;
    background: var(--primary-gradient);
    border: none;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 600;
    letter-spacing: 1px;
    transition: all var(--transition-normal) ease;
    box-shadow: var(--shadow-md);

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-lg);
      background: var(--secondary-gradient);
    }

    &:active {
      transform: translateY(0);
    }

    &.is-loading {
      background: var(--text-secondary);
      cursor: not-allowed;

      &:hover {
        transform: none;
        background: var(--text-secondary);
      }
    }
  }

  // 底部信息
  .login-footer {
    text-align: center;
    margin-top: 32px;
    padding-top: 24px;
    border-top: 1px solid var(--border-color);

    p {
      margin: 0;
      font-size: 12px;
      color: var(--text-secondary);
    }
  }

  // 响应式设计
  @media (max-width: 1024px) {
    .login-background {
      display: none;
    }

    .login-form-container {
      width: 100%;
      background: var(--primary-gradient);
    }
  }

  @media (max-width: 768px) {
    .login-form-wrapper {
      margin: 20px;
      padding: 32px 24px;
    }

    .background-content .welcome-text h1 {
      font-size: 36px;
    }

    .form-title h3 {
      font-size: 24px;
    }
  }
</style>
