<template>
  <div class="test-booking-page">
    <headers />
    
    <div class="test-content">
      <h1 class="page-title">🧪 预约功能测试页面</h1>
      
      <!-- 用户状态检查 -->
      <section class="test-section">
        <h2 class="section-title">用户登录状态检查</h2>
        <div class="status-info">
          <p><strong>用户ID:</strong> {{ userId || '未登录' }}</p>
          <p><strong>localStorage用户信息:</strong> {{ userInfoString || '无' }}</p>
          <el-button @click="createTestUser" type="primary">创建测试用户</el-button>
          <el-button @click="clearUser" type="danger">清除用户信息</el-button>
        </div>
      </section>

      <!-- API测试 -->
      <section class="test-section">
        <h2 class="section-title">API连接测试</h2>
        <div class="api-test">
          <el-button @click="testAPI" type="success" :loading="apiTesting">测试预约API</el-button>
          <div v-if="apiResult" class="api-result">
            <h4>API测试结果:</h4>
            <pre>{{ JSON.stringify(apiResult, null, 2) }}</pre>
          </div>
        </div>
      </section>

      <!-- 预约表单测试 -->
      <section class="test-section">
        <h2 class="section-title">预约表单测试</h2>
        <el-card class="booking-test-card">
          <el-form 
            :model="testBookingForm" 
            :rules="testBookingRules" 
            ref="testBookingFormRef" 
            label-width="120px"
          >
            <el-form-item label="景点ID" prop="attractionsId">
              <el-input v-model="testBookingForm.attractionsId" placeholder="输入景点ID"></el-input>
            </el-form-item>
            <el-form-item label="预约数量" prop="num">
              <el-input-number v-model="testBookingForm.num" :min="1" :max="10"></el-input-number>
            </el-form-item>
            <el-form-item label="预约日期" prop="time">
              <el-date-picker
                v-model="testBookingForm.time"
                type="date"
                placeholder="选择预约日期"
                value-format="yyyy-MM-dd"
                style="width: 100%;"
              />
            </el-form-item>
            <el-form-item label="游客信息" prop="people">
              <el-input 
                type="textarea" 
                v-model="testBookingForm.people" 
                placeholder='输入JSON格式的游客信息，例如：[{"name":"张三","tel":"13800138000","idCard":""}]'
                :rows="4"
              />
            </el-form-item>
            <el-form-item>
              <el-button 
                type="primary" 
                @click="submitTestBooking" 
                :loading="submitting"
              >
                提交测试预约
              </el-button>
              <el-button @click="fillTestData">填充测试数据</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </section>

      <!-- 控制台日志 -->
      <section class="test-section">
        <h2 class="section-title">控制台日志</h2>
        <div class="console-logs">
          <p>请打开浏览器开发者工具查看详细日志信息</p>
          <el-button @click="clearConsole" type="info">清除控制台</el-button>
        </div>
      </section>
    </div>
    
    <bottoms />
  </div>
</template>

<script>
import headers from '@/components/header'
import bottoms from '@/components/bottom'
import { saveSysAttractionOrder } from '@/api/api'

export default {
  name: 'TestBooking',
  components: {
    headers,
    bottoms
  },
  data() {
    return {
      apiTesting: false,
      submitting: false,
      apiResult: null,
      testBookingForm: {
        attractionsId: '',
        num: 1,
        time: '',
        people: ''
      },
      testBookingRules: {
        attractionsId: [{ required: true, message: '请输入景点ID', trigger: 'blur' }],
        num: [{ required: true, message: '请输入预约数量', trigger: 'change' }],
        time: [{ required: true, message: '请选择预约日期', trigger: 'change' }],
        people: [{ required: true, message: '请输入游客信息', trigger: 'blur' }]
      }
    }
  },
  computed: {
    userId() {
      const userInfoString = window.localStorage.getItem("user_info");
      if (userInfoString) {
        try { 
          return JSON.parse(userInfoString).id; 
        } catch(e) { 
          return null; 
        }
      }
      return null;
    },
    userInfoString() {
      return window.localStorage.getItem("user_info");
    }
  },
  methods: {
    createTestUser() {
      const testUser = { 
        id: 1, 
        username: 'test_user',
        name: '测试用户',
        email: '<EMAIL>'
      };
      window.localStorage.setItem("user_info", JSON.stringify(testUser));
      this.$message.success('测试用户创建成功');
      console.log('✅ 创建测试用户:', testUser);
    },
    
    clearUser() {
      window.localStorage.removeItem("user_info");
      this.$message.info('用户信息已清除');
      console.log('🗑️ 用户信息已清除');
    },
    
    async testAPI() {
      this.apiTesting = true;
      this.apiResult = null;
      
      try {
        const userInfo = this.getCurrentUserInfo();
        const currentTime = new Date().toISOString().slice(0, 19).replace('T', ' ');

        const testParams = {
          attractionsId: '1',
          num: 1,
          time: '2024-12-25',
          people: JSON.stringify([{name: 'API测试', tel: '13800138000', idCard: ''}]),
          userId: this.userId || 1,
          // ✅ 添加创建者和创建时间
          createBy: userInfo.finalUserName || 'API测试用户',
          createTime: currentTime
        };
        
        console.log('🧪 API测试参数:', testParams);
        const result = await saveSysAttractionOrder(testParams);
        console.log('📥 API测试结果:', result);
        
        this.apiResult = result;
        
        if (result && (result.code === 1000 || result.code === 200)) {
          this.$message.success('API测试成功');
        } else {
          this.$message.warning('API返回非成功状态: ' + (result.message || '未知错误'));
        }
      } catch (error) {
        console.error('❌ API测试失败:', error);
        this.apiResult = { error: error.message, stack: error.stack };
        this.$message.error('API测试失败: ' + error.message);
      } finally {
        this.apiTesting = false;
      }
    },
    
    fillTestData() {
      this.testBookingForm = {
        attractionsId: '1',
        num: 2,
        time: '2024-12-25',
        people: JSON.stringify([
          {name: '张三', tel: '13800138000', idCard: '110101199001011234'},
          {name: '李四', tel: '13900139000', idCard: '110101199002022345'}
        ])
      };
      this.$message.info('测试数据已填充');
    },
    
    submitTestBooking() {
      this.$refs.testBookingFormRef.validate(async (valid) => {
        if (valid) {
          this.submitting = true;
          
          try {
            let peopleData;
            try {
              peopleData = JSON.parse(this.testBookingForm.people);
            } catch (e) {
              this.$message.error('游客信息JSON格式错误');
              return;
            }
            
            const userInfo = this.getCurrentUserInfo();
            const currentTime = new Date().toISOString().slice(0, 19).replace('T', ' ');

            const params = {
              attractionsId: this.testBookingForm.attractionsId,
              num: this.testBookingForm.num,
              time: this.testBookingForm.time,
              people: this.testBookingForm.people,
              userId: this.userId || 1,
              // ✅ 添加创建者和创建时间
              createBy: userInfo.finalUserName || '测试用户',
              createTime: currentTime
            };
            
            console.log('📤 提交测试预约:', params);
            const result = await saveSysAttractionOrder(params);
            console.log('📥 预约结果:', result);
            
            if (result && (result.code === 1000 || result.code === 200)) {
              this.$message.success('测试预约提交成功');
            } else {
              this.$message.error('预约失败: ' + (result.message || '未知错误'));
            }
          } catch (error) {
            console.error('❌ 提交预约失败:', error);
            this.$message.error('提交失败: ' + error.message);
          } finally {
            this.submitting = false;
          }
        }
      });
    },
    
    clearConsole() {
      console.clear();
      this.$message.info('控制台已清除');
    },

    // 获取当前用户信息
    getCurrentUserInfo() {
      const userInfoString = window.localStorage.getItem("user_info");
      if (userInfoString) {
        try {
          const userInfo = JSON.parse(userInfoString);
          // 使用正确的字段名
          const userName = userInfo.userName || userInfo.loginAccount || userInfo.user_name || '测试用户';
          return {
            ...userInfo,
            finalUserName: userName
          };
        } catch (e) {
          console.error('解析用户信息失败:', e);
          return { userName: '测试用户', finalUserName: '测试用户' };
        }
      }
      return { userName: '测试用户', finalUserName: '测试用户' };
    }
  },
  
  mounted() {
    console.log('🧪 预约功能测试页面已加载');
    console.log('📍 当前路由:', this.$route);
    console.log('🏪 Store状态:', this.$store.state);
  }
}
</script>

<style scoped lang="scss">
.test-booking-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.test-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 40px 20px;
}

.page-title {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.test-section {
  margin-bottom: 40px;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.status-info {
  p {
    margin: 8px 0;
    font-size: 14px;
    
    strong {
      color: #374151;
    }
  }
  
  .el-button {
    margin-right: 12px;
    margin-top: 12px;
  }
}

.api-test {
  .api-result {
    margin-top: 16px;
    padding: 16px;
    background: #f3f4f6;
    border-radius: 8px;
    
    h4 {
      margin: 0 0 8px 0;
      color: #374151;
    }
    
    pre {
      background: #1f2937;
      color: #f9fafb;
      padding: 12px;
      border-radius: 6px;
      overflow-x: auto;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}

.booking-test-card {
  border-radius: 12px;
}

.console-logs {
  p {
    color: #6b7280;
    margin-bottom: 12px;
  }
}

@media (max-width: 768px) {
  .test-content {
    padding: 20px 16px;
  }
  
  .test-section {
    padding: 16px;
  }
}
</style>
