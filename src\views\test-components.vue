<template>
  <div class="test-components-page">
    <headers />
    
    <div class="test-content">
      <h1 class="page-title">🎨 UI组件测试页面</h1>
      
      <!-- 加载组件测试 -->
      <section class="test-section">
        <h2 class="section-title">加载组件测试</h2>
        <div class="component-demo">
          <div class="demo-item">
            <h3>点状加载</h3>
            <LoadingSpinner spinner-type="dots" text="加载中..." />
          </div>
          <div class="demo-item">
            <h3>脉冲加载</h3>
            <LoadingSpinner spinner-type="pulse" text="处理中..." />
          </div>
          <div class="demo-item">
            <h3>波浪加载</h3>
            <LoadingSpinner spinner-type="wave" text="请稍候..." />
          </div>
          <div class="demo-item">
            <h3>圆形加载</h3>
            <LoadingSpinner spinner-type="circle" text="正在加载..." />
          </div>
        </div>
      </section>

      <!-- 空状态组件测试 -->
      <section class="test-section">
        <h2 class="section-title">空状态组件测试</h2>
        <div class="component-demo">
          <div class="demo-item">
            <h3>默认空状态</h3>
            <EmptyState 
              title="暂无数据"
              description="当前没有找到相关信息"
            />
          </div>
          <div class="demo-item">
            <h3>带操作按钮</h3>
            <EmptyState 
              title="暂无景点信息"
              description="当前没有找到符合条件的景点"
              action-text="重新搜索"
              action-icon="el-icon-refresh"
              @action="handleAction"
            />
          </div>
          <div class="demo-item">
            <h3>自定义图标</h3>
            <EmptyState 
              title="网络连接失败"
              description="请检查您的网络连接"
              icon-class="el-icon-warning-outline"
              action-text="重试"
              action-type="warning"
              @action="handleAction"
            />
          </div>
        </div>
      </section>

      <!-- 现代化按钮测试 -->
      <section class="test-section">
        <h2 class="section-title">现代化按钮测试</h2>
        <div class="component-demo">
          <div class="demo-item">
            <h3>主要按钮</h3>
            <el-button type="primary" size="medium">主要按钮</el-button>
            <el-button type="primary" size="small">小按钮</el-button>
            <el-button type="primary" size="mini">迷你按钮</el-button>
          </div>
          <div class="demo-item">
            <h3>成功按钮</h3>
            <el-button type="success" size="medium">成功按钮</el-button>
            <el-button type="success" plain>朴素按钮</el-button>
          </div>
          <div class="demo-item">
            <h3>带图标按钮</h3>
            <el-button type="primary" icon="el-icon-search">搜索</el-button>
            <el-button type="success" icon="el-icon-check">确认</el-button>
            <el-button type="warning" icon="el-icon-warning">警告</el-button>
          </div>
        </div>
      </section>

      <!-- 现代化卡片测试 -->
      <section class="test-section">
        <h2 class="section-title">现代化卡片测试</h2>
        <div class="card-demo">
          <el-card class="demo-card" shadow="hover">
            <div slot="header" class="card-header">
              <span>卡片标题</span>
              <el-button style="float: right; padding: 3px 0" type="text">操作按钮</el-button>
            </div>
            <div class="card-content">
              这是一个现代化设计的卡片组件，具有圆角、阴影和悬浮效果。
            </div>
          </el-card>
          
          <el-card class="demo-card" shadow="hover">
            <div class="card-image">
              <el-image 
                src="https://picsum.photos/300/200" 
                fit="cover"
                style="width: 100%; height: 200px;"
              >
                <div slot="placeholder" class="image-placeholder">
                  <LoadingSpinner spinner-type="dots" size="small" text="" />
                </div>
              </el-image>
            </div>
            <div class="card-content">
              <h3>图片卡片</h3>
              <p>这是一个包含图片的现代化卡片设计。</p>
              <el-button type="primary" size="small">查看详情</el-button>
            </div>
          </el-card>
        </div>
      </section>

      <!-- 表单组件测试 -->
      <section class="test-section">
        <h2 class="section-title">表单组件测试</h2>
        <div class="form-demo">
          <el-form :model="testForm" label-width="100px">
            <el-form-item label="用户名">
              <el-input v-model="testForm.username" placeholder="请输入用户名"></el-input>
            </el-form-item>
            <el-form-item label="邮箱">
              <el-input v-model="testForm.email" placeholder="请输入邮箱"></el-input>
            </el-form-item>
            <el-form-item label="选择城市">
              <el-select v-model="testForm.city" placeholder="请选择城市">
                <el-option label="北京" value="beijing"></el-option>
                <el-option label="上海" value="shanghai"></el-option>
                <el-option label="广州" value="guangzhou"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary">提交</el-button>
              <el-button>重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </section>
    </div>
    
    <bottoms />
  </div>
</template>

<script>
import headers from '@/components/header'
import bottoms from '@/components/bottom'
import LoadingSpinner from '@/components/LoadingSpinner'
import EmptyState from '@/components/EmptyState'

export default {
  name: 'TestComponents',
  components: {
    headers,
    bottoms,
    LoadingSpinner,
    EmptyState
  },
  data() {
    return {
      testForm: {
        username: '',
        email: '',
        city: ''
      }
    }
  },
  methods: {
    handleAction() {
      this.$message.success('操作成功！')
    }
  }
}
</script>

<style scoped lang="scss">
.test-components-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.test-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.page-title {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.test-section {
  margin-bottom: 60px;
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e5e7eb;
}

.component-demo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.demo-item {
  padding: 20px;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  text-align: center;
  
  h3 {
    margin-bottom: 16px;
    color: #4b5563;
    font-size: 16px;
  }
}

.card-demo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.demo-card {
  border-radius: 16px;
  overflow: hidden;
  
  .card-header {
    font-weight: 600;
    color: #1f2937;
  }
  
  .card-content {
    padding: 16px 0;
    
    h3 {
      margin: 12px 0 8px 0;
      color: #1f2937;
    }
    
    p {
      color: #6b7280;
      margin-bottom: 16px;
    }
  }
}

.form-demo {
  max-width: 500px;
  margin: 0 auto;
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  background: #f3f4f6;
}

@media (max-width: 768px) {
  .test-content {
    padding: 20px 16px;
  }
  
  .test-section {
    padding: 20px;
  }
  
  .component-demo {
    grid-template-columns: 1fr;
  }
  
  .card-demo {
    grid-template-columns: 1fr;
  }
}
</style>
