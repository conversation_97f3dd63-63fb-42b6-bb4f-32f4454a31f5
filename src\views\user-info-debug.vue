<template>
  <div class="user-debug-page">
    <headers />
    
    <div class="debug-content">
      <h1 class="page-title">🔍 用户信息调试页面</h1>
      
      <!-- 当前用户信息状态 -->
      <section class="debug-section">
        <h2 class="section-title">当前用户信息状态</h2>
        <div class="info-display">
          <div class="info-item">
            <label>localStorage 原始数据:</label>
            <pre class="code-block">{{ userInfoRaw || '无数据' }}</pre>
          </div>
          
          <div class="info-item" v-if="userInfoParsed">
            <label>解析后的用户信息:</label>
            <pre class="code-block">{{ JSON.stringify(userInfoParsed, null, 2) }}</pre>
          </div>
          
          <div class="info-item">
            <label>用户ID:</label>
            <span class="value">{{ userId || '未获取到' }}</span>
          </div>
          
          <div class="info-item">
            <label>用户名 (user_name):</label>
            <span class="value">{{ userInfoParsed?.user_name || '未设置' }}</span>
          </div>
          
          <div class="info-item">
            <label>登录账号 (login_account):</label>
            <span class="value">{{ userInfoParsed?.login_account || '未设置' }}</span>
          </div>
          
          <div class="info-item">
            <label>用户Token:</label>
            <span class="value">{{ userToken || '未设置' }}</span>
          </div>

          <div class="info-item">
            <label>最终使用的用户名:</label>
            <span class="value final-name">{{ finalUserName }}</span>
          </div>
        </div>
      </section>

      <!-- 用户信息字段检查 -->
      <section class="debug-section">
        <h2 class="section-title">用户信息字段检查</h2>
        <div class="field-check">
          <div class="check-item" :class="{ 'has-value': userInfoParsed?.user_name }">
            <i class="el-icon-check" v-if="userInfoParsed?.user_name"></i>
            <i class="el-icon-close" v-else></i>
            <span>user_name: {{ userInfoParsed?.user_name || '❌ 缺失' }}</span>
          </div>
          
          <div class="check-item" :class="{ 'has-value': userInfoParsed?.username }">
            <i class="el-icon-check" v-if="userInfoParsed?.username"></i>
            <i class="el-icon-close" v-else></i>
            <span>username: {{ userInfoParsed?.username || '❌ 缺失' }}</span>
          </div>
          
          <div class="check-item" :class="{ 'has-value': userInfoParsed?.name }">
            <i class="el-icon-check" v-if="userInfoParsed?.name"></i>
            <i class="el-icon-close" v-else></i>
            <span>name: {{ userInfoParsed?.name || '❌ 缺失' }}</span>
          </div>
          
          <div class="check-item" :class="{ 'has-value': userInfoParsed?.login_account }">
            <i class="el-icon-check" v-if="userInfoParsed?.login_account"></i>
            <i class="el-icon-close" v-else></i>
            <span>login_account: {{ userInfoParsed?.login_account || '❌ 缺失' }}</span>
          </div>
        </div>
      </section>

      <!-- 操作按钮 -->
      <section class="debug-section">
        <h2 class="section-title">调试操作</h2>
        <div class="action-buttons">
          <el-button @click="refreshUserInfo" type="primary">刷新用户信息</el-button>
          <el-button @click="clearUserInfo" type="danger">清除用户信息</el-button>
          <el-button @click="createTestUser" type="success">创建测试用户</el-button>
          <el-button @click="simulateLogin" type="warning">模拟登录</el-button>
          <el-button @click="testToken" type="info">测试Token有效性</el-button>
        </div>
      </section>

      <!-- 预约测试 -->
      <section class="debug-section">
        <h2 class="section-title">预约参数测试</h2>
        <div class="booking-test">
          <el-button @click="testBookingParams" type="info" :loading="testing">
            测试预约参数生成
          </el-button>
          
          <div v-if="testResult" class="test-result">
            <h4>生成的预约参数:</h4>
            <pre class="code-block">{{ JSON.stringify(testResult, null, 2) }}</pre>
          </div>
        </div>
      </section>

      <!-- 解决方案建议 -->
      <section class="debug-section">
        <h2 class="section-title">问题诊断和解决方案</h2>
        <div class="diagnosis">
          <div v-if="!userToken" class="issue">
            <h4>❌ 问题：没有用户Token</h4>
            <p>localStorage中没有找到user_token数据</p>
            <p><strong>解决方案：</strong>请先登录系统获取Token</p>
          </div>

          <div v-else-if="!userInfoRaw" class="issue">
            <h4>❌ 问题：没有用户信息</h4>
            <p>localStorage中没有找到user_info数据</p>
            <p><strong>解决方案：</strong>请先登录系统</p>
          </div>
          
          <div v-else-if="!userInfoParsed" class="issue">
            <h4>❌ 问题：用户信息格式错误</h4>
            <p>localStorage中的user_info数据无法解析</p>
            <p><strong>解决方案：</strong>清除用户信息后重新登录</p>
          </div>
          
          <div v-else-if="!userInfoParsed.user_name && !userInfoParsed.username && !userInfoParsed.name" class="issue">
            <h4>❌ 问题：缺少用户名字段</h4>
            <p>用户信息中没有包含用户名相关字段</p>
            <p><strong>解决方案：</strong>检查后端API返回的用户信息结构</p>
          </div>
          
          <div v-else class="success">
            <h4>✅ 用户信息正常</h4>
            <p>用户信息获取正常，预约时应该能正确记录用户名</p>
          </div>
        </div>
      </section>
    </div>
    
    <bottoms />
  </div>
</template>

<script>
import headers from '@/components/header'
import bottoms from '@/components/bottom'

export default {
  name: 'UserInfoDebug',
  components: {
    headers,
    bottoms
  },
  data() {
    return {
      testing: false,
      testResult: null
    }
  },
  computed: {
    userInfoRaw() {
      return window.localStorage.getItem("user_info");
    },
    userToken() {
      return window.localStorage.getItem("user_token");
    },
    userInfoParsed() {
      if (this.userInfoRaw) {
        try {
          return JSON.parse(this.userInfoRaw);
        } catch (e) {
          return null;
        }
      }
      return null;
    },
    userId() {
      return this.userInfoParsed?.id;
    },
    finalUserName() {
      if (!this.userInfoParsed) return '游客';
      return this.userInfoParsed.user_name || 
             this.userInfoParsed.username || 
             this.userInfoParsed.name || 
             this.userInfoParsed.login_account || 
             '游客';
    }
  },
  methods: {
    refreshUserInfo() {
      this.$forceUpdate();
      this.$message.info('用户信息已刷新');
    },
    
    clearUserInfo() {
      window.localStorage.removeItem("user_info");
      window.localStorage.removeItem("user_token");
      this.$message.success('用户信息已清除');
      this.$forceUpdate();
    },
    
    createTestUser() {
      const testUser = {
        id: '1',
        user_name: '测试用户',
        login_account: 'test_user',
        email: '<EMAIL>',
        tel: '***********'
      };
      window.localStorage.setItem("user_info", JSON.stringify(testUser));
      this.$message.success('测试用户已创建');
      this.$forceUpdate();
    },
    
    simulateLogin() {
      // 模拟真实的用户登录数据结构
      const realUser = {
        id: '0de96461b6ef0328cef416dea9366c9c',
        user_name: '杭州水果捞',
        login_account: 'user',
        user_type: 1,
        email: '<EMAIL>',
        tel: '***********',
        sex: 0,
        avatar: '/img/*************.jpg',
        status: 0,
        create_time: '2024-04-09 21:34:26'
      };
      window.localStorage.setItem("user_info", JSON.stringify(realUser));
      window.localStorage.setItem("user_token", "mock_token_123456");
      this.$message.success('模拟登录成功');
      this.$forceUpdate();
    },
    
    testBookingParams() {
      this.testing = true;
      
      setTimeout(() => {
        const userInfo = this.userInfoParsed;
        const currentTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
        
        this.testResult = {
          attractionsId: '1',
          num: 2,
          time: '2024-12-25',
          people: JSON.stringify([
            {name: '张三', tel: '***********', idCard: '110101199001011234'},
            {name: '李四', tel: '***********', idCard: '110101199002022345'}
          ]),
          userId: this.userId,
          createBy: this.finalUserName,
          createTime: currentTime
        };
        
        this.testing = false;
        this.$message.success('预约参数生成完成');
      }, 1000);
    },

    async testToken() {
      const token = this.userToken;
      if (!token) {
        this.$message.error('没有找到Token，请先登录');
        return;
      }

      try {
        // 测试Token有效性 - 调用一个需要认证的API
        const response = await fetch('/api/user/info', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'x_access_token': token
          }
        });

        const result = await response.json();
        console.log('Token测试结果:', result);

        if (result.code === 200) {
          this.$message.success('Token有效，用户信息获取成功');
          console.log('从Token获取的用户信息:', result.data);
        } else {
          this.$message.error('Token无效: ' + (result.message || '未知错误'));
        }
      } catch (error) {
        console.error('Token测试失败:', error);
        this.$message.error('Token测试失败: ' + error.message);
      }
    }
  }
}
</script>

<style scoped lang="scss">
.user-debug-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.debug-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 40px 20px;
}

.page-title {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.debug-section {
  margin-bottom: 40px;
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e5e7eb;
}

.info-display {
  .info-item {
    margin-bottom: 16px;
    
    label {
      display: block;
      font-weight: 500;
      color: #374151;
      margin-bottom: 4px;
    }
    
    .value {
      color: #6b7280;
      
      &.final-name {
        font-weight: 600;
        color: #059669;
        font-size: 16px;
      }
    }
  }
}

.code-block {
  background: #1f2937;
  color: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
}

.field-check {
  .check-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    padding: 8px;
    border-radius: 6px;
    
    &.has-value {
      background: #ecfdf5;
      color: #059669;
    }
    
    &:not(.has-value) {
      background: #fef2f2;
      color: #dc2626;
    }
    
    i {
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  
  .el-button {
    border-radius: 8px;
  }
}

.booking-test {
  .test-result {
    margin-top: 16px;
    
    h4 {
      margin: 0 0 8px 0;
      color: #374151;
    }
  }
}

.diagnosis {
  .issue, .success {
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 12px;
    
    h4 {
      margin: 0 0 8px 0;
    }
    
    p {
      margin: 4px 0;
    }
  }
  
  .issue {
    background: #fef2f2;
    border-left: 4px solid #dc2626;
  }
  
  .success {
    background: #ecfdf5;
    border-left: 4px solid #059669;
  }
}

@media (max-width: 768px) {
  .debug-content {
    padding: 20px 16px;
  }
  
  .debug-section {
    padding: 16px;
  }
  
  .action-buttons {
    .el-button {
      width: 100%;
      margin-bottom: 8px;
    }
  }
}
</style>
