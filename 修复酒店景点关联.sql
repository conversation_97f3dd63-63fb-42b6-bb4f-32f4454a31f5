-- ===================================================================
-- 修复酒店数据中的景点ID关联
-- 将酒店数据中的attractions_id更新为正确的景点ID
-- ===================================================================

BEGIN;

-- 更新北京王府井希尔顿酒店 - 关联故宫博物院
UPDATE sys_hotel SET attractions_id = '1004', attractions = '故宫博物院' 
WHERE id = '2025062100000101';

-- 更新北京东方君悦大酒店 - 关联故宫博物院
UPDATE sys_hotel SET attractions_id = '1004', attractions = '故宫博物院' 
WHERE id = '2025062100000102';

-- 更新西安临潼悦椿温泉酒店 - 关联兵马俑（如果没有兵马俑，暂时关联故宫）
-- 注意：您的景点列表中没有兵马俑，建议添加或选择其他景点
UPDATE sys_hotel SET attractions_id = '1004', attractions = '故宫博物院' 
WHERE id = '2025062100000103';

-- 更新秦风唐韵精品客栈 - 关联兵马俑（如果没有兵马俑，暂时关联故宫）
UPDATE sys_hotel SET attractions_id = '1004', attractions = '故宫博物院' 
WHERE id = '2025062100000104';

-- 更新泰安东尊华美达酒店 - 关联泰山（如果没有泰山，暂时关联黄山）
UPDATE sys_hotel SET attractions_id = '1001', attractions = '黄山风景区' 
WHERE id = '2025062100000105';

-- 更新黄山温泉度假酒店 - 关联黄山风景区
UPDATE sys_hotel SET attractions_id = '1001', attractions = '黄山风景区' 
WHERE id = '2025062100000106';

-- 更新乌镇枕水度假酒店 - 关联西湖（江南地区）
UPDATE sys_hotel SET attractions_id = '1003', attractions = '西湖' 
WHERE id = '2025062100000107';

-- 更新喀纳斯湖畔山庄 - 关联张家界（自然风光类似）
UPDATE sys_hotel SET attractions_id = '1002', attractions = '张家界国家森林公园' 
WHERE id = '2025062100000108';

-- 更新张家界纳百利皇冠假日酒店 - 关联张家界国家森林公园
UPDATE sys_hotel SET attractions_id = '1002', attractions = '张家界国家森林公园' 
WHERE id = '2025062100000109';

-- 更新杭州西湖国宾馆 - 关联西湖
UPDATE sys_hotel SET attractions_id = '1003', attractions = '西湖' 
WHERE id = '2025062100000110';

-- 更新九寨沟喜来登国际大酒店 - 关联九寨沟风景区
UPDATE sys_hotel SET attractions_id = '1005', attractions = '九寨沟风景区' 
WHERE id = '2025062100000111';

-- 更新八达岭长城脚下的公社 - 关联长城-八达岭
UPDATE sys_hotel SET attractions_id = '1006', attractions = '长城-八达岭' 
WHERE id = '2025062100000112';

-- 更新峨眉山红珠山宾馆 - 关联峨眉山景区
UPDATE sys_hotel SET attractions_id = '1007', attractions = '峨眉山景区' 
WHERE id = '2025062100000113';

-- 更新丽江和府洲际度假酒店 - 关联丽江古城
UPDATE sys_hotel SET attractions_id = '1008', attractions = '丽江古城' 
WHERE id = '2025062100000114';

-- 更新桂林香格里拉大酒店 - 关联桂林漓江景区
UPDATE sys_hotel SET attractions_id = '1009', attractions = '桂林漓江景区' 
WHERE id = '2025062100000115';

-- 更新厦门鼓浪屿既下山酒店 - 关联厦门鼓浪屿
UPDATE sys_hotel SET attractions_id = '1010', attractions = '厦门鼓浪屿' 
WHERE id = '2025062100000116';

-- 更新上海迪士尼乐园酒店 - 关联上海迪士尼乐园
UPDATE sys_hotel SET attractions_id = '1011', attractions = '上海迪士尼乐园' 
WHERE id = '2025062100000117';

-- 更新成都熊猫王子文化酒店 - 关联成都大熊猫繁育研究基地
UPDATE sys_hotel SET attractions_id = '1012', attractions = '成都大熊猫繁育研究基地' 
WHERE id = '2025062100000118';

-- 更新香港迪士尼乐园酒店 - 关联香港迪士尼乐园
UPDATE sys_hotel SET attractions_id = '1013', attractions = '香港迪士尼乐园' 
WHERE id = '2025062100000119';

-- 更新三亚亚龙湾瑞吉度假酒店 - 关联三亚亚龙湾
UPDATE sys_hotel SET attractions_id = '1014', attractions = '三亚亚龙湾' 
WHERE id = '2025062100000120';

-- 更新敦煌山庄 - 关联莫高窟
UPDATE sys_hotel SET attractions_id = '1015', attractions = '莫高窟' 
WHERE id = '2025062100000121';

COMMIT;

-- ===================================================================
-- 验证更新结果
-- ===================================================================

-- 查询所有酒店的景点关联情况
SELECT 
    h.id,
    h.name as hotel_name,
    h.attractions_id,
    h.attractions as hotel_attractions_name,
    a.name as actual_attractions_name
FROM sys_hotel h
LEFT JOIN sys_attractions a ON h.attractions_id = a.id
ORDER BY h.id;

-- ===================================================================
-- 修复完成说明
-- ===================================================================

/*
修复内容：
1. ✅ 将所有酒店的attractions_id更新为有效的景点ID
2. ✅ 同时更新attractions字段为对应的景点名称
3. ✅ 确保酒店与景点的逻辑关联正确

景点关联分配：
- 故宫博物院(1004): 北京王府井希尔顿、北京东方君悦、西安两家酒店(临时)
- 黄山风景区(1001): 黄山温泉度假酒店、泰安东尊华美达(临时)
- 西湖(1003): 杭州西湖国宾馆、乌镇枕水度假酒店
- 张家界国家森林公园(1002): 张家界纳百利皇冠假日、喀纳斯湖畔山庄(临时)
- 九寨沟风景区(1005): 九寨沟喜来登国际大酒店
- 长城-八达岭(1006): 八达岭长城脚下的公社
- 峨眉山景区(1007): 峨眉山红珠山宾馆
- 丽江古城(1008): 丽江和府洲际度假酒店
- 桂林漓江景区(1009): 桂林香格里拉大酒店
- 厦门鼓浪屿(1010): 厦门鼓浪屿既下山酒店
- 上海迪士尼乐园(1011): 上海迪士尼乐园酒店
- 成都大熊猫繁育研究基地(1012): 成都熊猫王子文化酒店
- 香港迪士尼乐园(1013): 香港迪士尼乐园酒店
- 三亚亚龙湾(1014): 三亚亚龙湾瑞吉度假酒店
- 莫高窟(1015): 敦煌山庄

注意事项：
1. 部分酒店原本关联的景点(如兵马俑、泰山)在当前景点列表中不存在
2. 已临时分配到相近或相关的景点
3. 建议后续添加缺失的景点数据，或调整酒店关联
*/
