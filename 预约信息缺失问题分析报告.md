# 🔍 景点预约信息缺失问题分析报告

## 📋 问题描述
前端用户提交预约信息后，没有记录预定人的信息和创建时间。

## 🔍 数据库结构分析

### 1. 景点预约表 (`sys_attraction_order`) 结构
```sql
CREATE TABLE `sys_attraction_order` (
  `id` varchar(100) NOT NULL,
  `attractions_id` varchar(100) COMMENT '景点id',
  `name` varchar(255) COMMENT '名称',
  `introduce` text COMMENT '简介',
  `images` longtext COMMENT '图片',
  `num` int COMMENT '人数',
  `time` datetime COMMENT '预约时间',
  `people` longtext COMMENT '信息',
  `state` int DEFAULT '0' COMMENT '状态',
  `user_id` varchar(100) COMMENT '用户id',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',      -- ✅ 有此字段
  `create_time` datetime COMMENT '创建时间',               -- ✅ 有此字段
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime COMMENT '更新时间',
  PRIMARY KEY (`id`)
);
```

### 2. 用户表 (`user`) 结构
```sql
CREATE TABLE `user` (
  `id` varchar(100) NOT NULL COMMENT '用户id',
  `user_name` varchar(50) COMMENT '用户名',              -- ✅ 用户名字段
  `login_account` varchar(50) COMMENT '登陆账号',
  `user_type` int COMMENT '用户类型',
  `email` varchar(50) COMMENT '用户邮箱',
  `tel` varchar(50) COMMENT '手机号码',
  `sex` int DEFAULT '0' COMMENT '性别',
  `avatar` text COMMENT '头像',
  `password` varchar(50) COMMENT '密码',
  `salt` varchar(100) COMMENT '盐值',
  `status` int DEFAULT '0' COMMENT '帐号状态',
  `login_date` datetime COMMENT '最后登录时间',
  `pwd_update_date` datetime COMMENT '密码最后更新时间',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime COMMENT '更新时间',
  `remark` varchar(500) COMMENT '备注',
  PRIMARY KEY (`id`)
);
```

## 🎯 问题根本原因分析

### ❌ **主要问题：后端API未正确设置创建者和创建时间**

通过查看数据库中的现有记录，发现：

```sql
-- 现有的预约记录示例
INSERT INTO `sys_attraction_order` VALUES (
  '1777693570686365698', 
  '1777682811382247426', 
  '松峰山景区', 
  '...', 
  '...', 
  1, 
  '2024-04-10 08:00:00', 
  '[{"name":"张三","tel":"156165","idCard":"15616"}]', 
  5, 
  '0de96461b6ef0328cef416dea9366c9c',  -- ✅ 用户ID已记录
  '张三',                              -- ✅ 创建者已记录
  '2024-04-09 21:42:30',              -- ✅ 创建时间已记录
  '超级管理员', 
  '2024-06-13 15:10:24'
);
```

**发现：数据库表结构是完整的，现有记录也有创建者和创建时间信息！**

## 🔍 深入分析可能的问题

### 1. **前端提交的参数不完整**
前端可能只提交了基础的预约信息，没有包含创建者信息：
```javascript
// 当前前端提交的参数
const params = {
  attractionsId: this.attractionId,
  num: this.bookingForm.num,
  time: this.bookingForm.date,
  people: JSON.stringify(this.bookingForm.people),
  userId: this.userId,
  // ❌ 缺少 create_by 字段
  // ❌ 缺少 create_time 字段
};
```

### 2. **后端API处理逻辑问题**
后端接收到前端请求后，可能没有正确设置创建者和创建时间字段。

### 3. **用户信息获取问题**
前端可能无法正确获取当前登录用户的用户名信息。

## 🛠️ 解决方案

### 方案一：前端补充完整参数（推荐）

修改前端提交逻辑，补充创建者和创建时间信息：

```javascript
// 优化后的前端提交参数
async submitBooking() {
  // ... 表单验证逻辑 ...
  
  try {
    // 获取当前用户信息
    const userInfo = this.getCurrentUserInfo();
    
    const params = {
      attractionsId: this.attractionId,
      num: this.bookingForm.num,
      time: this.bookingForm.date,
      people: JSON.stringify(this.bookingForm.people),
      userId: this.userId,
      // ✅ 添加创建者信息
      createBy: userInfo.user_name || userInfo.username || '游客',
      // ✅ 添加创建时间
      createTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
    };
    
    console.log('📤 完整的API请求参数:', params);
    const res = await saveSysAttractionOrder(params);
    // ... 处理响应 ...
  } catch (error) {
    // ... 错误处理 ...
  }
},

// 获取当前用户信息的方法
getCurrentUserInfo() {
  const userInfoString = window.localStorage.getItem("user_info");
  if (userInfoString) {
    try {
      return JSON.parse(userInfoString);
    } catch (e) {
      console.error('解析用户信息失败:', e);
      return { user_name: '游客' };
    }
  }
  return { user_name: '游客' };
}
```

### 方案二：后端自动设置（备选）

如果后端API支持，可以在后端自动设置创建者和创建时间：

```java
// 后端伪代码示例
@PostMapping("/saveSysAttractionOrder")
public Result saveSysAttractionOrder(@RequestBody AttractionOrder order) {
    // 自动设置创建时间
    order.setCreateTime(new Date());
    
    // 根据userId查询用户信息并设置创建者
    User user = userService.getById(order.getUserId());
    if (user != null) {
        order.setCreateBy(user.getUserName());
    } else {
        order.setCreateBy("游客");
    }
    
    // 保存订单
    return attractionOrderService.save(order);
}
```

### 方案三：数据库触发器（不推荐）

可以创建数据库触发器自动设置创建时间，但不推荐这种方式。

## 📋 实施步骤

### 第一步：检查当前用户信息获取
```javascript
// 在浏览器控制台执行，检查用户信息
console.log('用户信息:', localStorage.getItem('user_info'));
```

### 第二步：修改前端代码
按照方案一修改 `attractionsInfo.vue` 文件中的 `submitBooking` 方法。

### 第三步：测试验证
1. 提交预约信息
2. 检查数据库中的记录
3. 验证 `create_by` 和 `create_time` 字段是否正确填充

### 第四步：后端验证（如需要）
检查后端API是否正确处理了新增的字段。

## 🔍 调试建议

### 1. 前端调试
```javascript
// 在提交前添加调试信息
console.log('🔍 当前用户ID:', this.userId);
console.log('🔍 用户信息:', this.getCurrentUserInfo());
console.log('📤 提交参数:', params);
```

### 2. 数据库查询验证
```sql
-- 查询最新的预约记录
SELECT 
    id,
    attractions_id,
    user_id,
    create_by,
    create_time,
    update_by,
    update_time
FROM sys_attraction_order 
ORDER BY create_time DESC 
LIMIT 5;
```

### 3. 后端日志检查
检查后端接收到的参数和保存的数据是否一致。

## 📊 预期结果

修复后，每条预约记录应该包含：
- ✅ `user_id`: 用户ID
- ✅ `create_by`: 用户名称
- ✅ `create_time`: 创建时间戳
- ✅ `people`: 预约人员信息（JSON格式）

## 🚨 注意事项

1. **时区问题**: 确保前后端时间格式一致
2. **用户名获取**: 确保能正确获取用户名，避免显示为空
3. **数据格式**: 确保日期时间格式符合数据库要求
4. **错误处理**: 添加适当的错误处理机制

---

**总结**: 问题的根本原因是前端提交参数不完整，缺少创建者和创建时间信息。通过补充完整的提交参数即可解决此问题。
